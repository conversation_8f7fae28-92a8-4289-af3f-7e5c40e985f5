# 设计文档

## 概述

本设计文档描述了基于 LangChain.js 和 LangGraph 的多智能体系统 MVP 的技术架构。系统采用工作流编排模式，通过 LangGraph 管理多个智能体节点的协作执行，支持条件分支、数据持久化和实时监控。

## 架构

### 整体架构

```mermaid
graph TB
    API[REST API 层] --> WF[工作流管理器]
    WF --> LG[LangGraph 引擎]
    
    LG --> NA[Agent A - LLM Agent]
    LG --> NB[Agent B - LLM Agent]
    NA --> NC[Agent C - Function Agent]
    NC --> ND[Agent D - LLM Agent]
    NB --> ND
    
    WF --> MON[监控服务]
    WF --> STORE[存储服务]
    STORE --> MONGO[(MongoDB)]
    STORE --> CACHE[本地缓存]
    
    NA --> SANDBOX1[沙箱环境A]
    NB --> SANDBOX2[沙箱环境B]
    NC --> SANDBOX3[沙箱环境C]
    ND --> SANDBOX4[沙箱环境D]
    
    subgraph "并行执行"
        NA
        NB
    end
    
    subgraph "汇聚执行"
        ND
    end
```

### 工作流执行流程

基于提供的 LangGraph 设计图，工作流执行流程如下：

```mermaid
graph TD
    START([__start__]) --> NA[agent_a]
    START --> NB[agent_b]
    NA --> NC[agent_c]
    NC --> ND[agent_d]
    NB --> ND[agent_d]
    ND --> END([__end__])
```

**流程说明：**
1. **__start__**: 工作流入口点，初始化执行上下文
2. **并行执行阶段**:
   - **agent_a**: 第一个 LLM 智能体，与 agent_b 同时开始执行
   - **agent_b**: 第二个 LLM 智能体，与 agent_a 并行执行，支持多次 LLM 调用
3. **顺序执行阶段**:
   - **agent_c**: 函数智能体，等待 agent_a 完成后执行，可以获取 agent_a 的执行结果，执行 `codeA + "xxxxdx"` 函数调用
4. **汇聚阶段**:
   - **agent_d**: 最终的 LLM 智能体，等待 agent_b 和 agent_c 都完成后执行，整合所有前面节点的结果
5. **__end__**: 工作流结束点，输出最终结果

**执行时序：**
- t1: agent_a 和 agent_b 同时开始执行
- t2: agent_a 完成后，agent_c 开始执行（可访问 agent_a 的结果）
- t3: 当 agent_b 和 agent_c 都完成后，agent_d 开始执行
- t4: agent_d 完成，工作流结束

## 组件和接口

### 1. 工作流管理器 (WorkflowManager)

**职责：**
- 管理 LangGraph 工作流的生命周期
- 协调各个智能体节点的执行
- 处理条件分支逻辑

**接口：**
```typescript
interface WorkflowManager {
  initializeWorkflow(): Promise<void>
  executeWorkflow(input: WorkflowInput): Promise<WorkflowResult>
  stopWorkflow(workflowId: string): Promise<void>
  getWorkflowStatus(workflowId: string): Promise<WorkflowStatus>
}
```

### 2. 智能体节点 (Agent Nodes)

**NodeA, NodeB, NodeD - LLM 智能体：**
```typescript
interface LLMAgent {
  execute(input: AgentInput): Promise<AgentOutput>
  getModel(): string
  getSandboxPath(): string
}
```

**NodeC - 函数智能体：**
```typescript
interface FunctionAgent {
  execute(input: AgentInput): Promise<AgentOutput>
  executeFunction(codeA: string): Promise<string>
}
```

### 3. 存储服务 (StorageService)

**职责：**
- 管理 MongoDB 连接和数据操作
- 提供本地缓存机制
- 支持历史数据查询

**接口：**
```typescript
interface StorageService {
  saveExecutionResult(result: ExecutionResult): Promise<void>
  queryResults(filter: QueryFilter): Promise<ExecutionResult[]>
  initializeCache(): void
  getCachedResult(key: string): ExecutionResult | null
}
```

### 4. 监控服务 (MonitoringService)

**职责：**
- 实时监控工作流执行状态
- 发送告警通知
- 生成执行报告

**接口：**
```typescript
interface MonitoringService {
  startMonitoring(workflowId: string): void
  updateNodeStatus(nodeId: string, status: NodeStatus): void
  sendAlert(alert: Alert): void
  generateReport(workflowId: string): Promise<ExecutionReport>
}
```

### 5. REST API 控制器

**端点设计：**
- `POST /api/workflows` - 启动工作流
- `GET /api/workflows/{id}` - 查询工作流状态
- `DELETE /api/workflows/{id}` - 停止工作流
- `GET /api/workflows/{id}/report` - 获取执行报告
- `GET /api/results` - 查询历史结果

## 数据模型

### 工作流执行结果 (ExecutionResult)

```typescript
interface ExecutionResult {
  workflowId: string
  nodeId: string
  nodeType: 'llm' | 'function'
  startTime: Date
  endTime: Date
  input: any
  output: any
  status: 'success' | 'error' | 'skipped'
  errorMessage?: string
  executionPath: string
}
```

### 工作流状态 (WorkflowStatus)

```typescript
interface WorkflowStatus {
  workflowId: string
  status: 'running' | 'completed' | 'failed' | 'stopped'
  currentNode?: string
  progress: number
  startTime: Date
  endTime?: Date
  nodes: NodeStatus[]
}
```

### 节点状态 (NodeStatus)

```typescript
interface NodeStatus {
  nodeId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  startTime?: Date
  endTime?: Date
  output?: any
  errorMessage?: string
}
```

## 错误处理

### 错误分类和处理策略

1. **LLM 调用错误**
   - 重试机制：最多重试 3 次
   - 降级策略：使用备用模型
   - 记录错误日志并继续流程

2. **数据库连接错误**
   - 自动切换到本地缓存
   - 定期尝试重连数据库
   - 缓存数据在连接恢复后同步

3. **沙箱环境错误**
   - 隔离错误影响范围
   - 重新初始化沙箱环境
   - 记录安全事件

4. **工作流执行错误**
   - 支持从失败点恢复
   - 保存中间状态
   - 提供手动干预接口

### 错误恢复机制

```typescript
interface ErrorRecovery {
  retryNode(nodeId: string, maxRetries: number): Promise<boolean>
  skipNode(nodeId: string, reason: string): Promise<void>
  rollbackToCheckpoint(checkpointId: string): Promise<void>
  manualIntervention(workflowId: string): Promise<void>
}
```

## 测试策略

### 单元测试
- 每个智能体节点的独立测试
- 存储服务的数据操作测试
- 监控服务的状态更新测试
- API 端点的输入输出验证

### 集成测试
- LangGraph 工作流的端到端执行
- 多智能体协作场景测试
- 条件分支逻辑验证
- 数据持久化完整性测试

### 性能测试
- 并发工作流执行测试
- 大量数据存储性能测试
- 内存使用和资源消耗监控

### 安全测试
- 沙箱环境隔离性验证
- 未授权访问防护测试
- 输入数据安全性检查

## 技术选型

### 核心框架
- **LangChain.js**: 智能体框架和 LLM 集成
- **LangGraph**: 工作流编排和状态管理
- **Node.js**: 运行时环境
- **TypeScript**: 开发语言

### 数据存储
- **MongoDB**: 主要数据持久化
- **Redis**: 本地缓存和会话管理

### 监控和日志
- **Winston**: 日志记录
- **Prometheus**: 指标收集
- **Grafana**: 监控面板

### API 框架
- **Express.js**: REST API 服务
- **Joi**: 输入验证
- **Swagger**: API 文档

## 部署架构

### 开发环境
- 本地 MongoDB 实例
- 本地 Redis 缓存
- 文件系统沙箱目录

### 生产环境考虑
- MongoDB 集群部署
- Redis 集群配置
- 容器化部署支持
- 负载均衡和高可用

## 安全考虑

### 沙箱环境实现
- 为每个智能体创建独立的工作目录
- 使用文件系统权限限制访问范围
- 监控和记录所有文件操作

### API 安全
- 输入参数验证和清理
- 请求频率限制
- 错误信息脱敏处理

### 数据安全
- 敏感数据加密存储
- 访问日志记录
- 定期安全审计