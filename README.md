# LangGraph 多智能体系统 MVP

基于 LangChain.js 和 LangGraph 的多智能体协作系统最小可行产品。

## 项目概述

本项目实现了一个多智能体工作流系统，支持：
- 多个智能体节点的并行和顺序执行
- 基于 LangGraph 的工作流编排
- 数据持久化和实时监控
- REST API 接口

## 技术栈

- **运行时**: Node.js + TypeScript
- **框架**: Express.js
- **智能体**: LangChain.js + LangGraph
- **数据库**: MongoDB
- **缓存**: Redis
- **测试**: Vitest
- **日志**: Winston

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
- `LLM_API_KEY`: LLM 服务的 API 密钥
- `MONGODB_URI`: MongoDB 连接字符串
- `REDIS_HOST`: Redis 服务器地址

### 3. 启动开发服务器

```bash
npm run dev
```

### 4. 运行测试

```bash
npm test
```

### 5. 构建项目

```bash
npm run build
npm start
```

## API 接口

### 启动工作流
```
POST /api/workflows
Content-Type: application/json

{
  "parameters": {
    "input": "your input data"
  }
}
```

### 查询工作流状态
```
GET /api/workflows/{workflowId}
```

### 停止工作流
```
DELETE /api/workflows/{workflowId}
```

### 获取执行报告
```
GET /api/workflows/{workflowId}/report
```

## 项目结构

```
src/
├── config/          # 配置管理
├── middleware/      # Express 中间件
├── routes/          # API 路由
├── services/        # 业务逻辑服务
├── types/           # TypeScript 类型定义
├── utils/           # 工具函数
├── scripts/         # 脚本文件
└── __tests__/       # 测试文件
```

## 开发指南

### 添加新的智能体节点

1. 在 `src/agents/` 目录下创建新的智能体类
2. 实现 `AgentInput` 和 `AgentOutput` 接口
3. 在工作流中注册新节点

### 添加新的 API 端点

1. 在 `src/routes/` 目录下添加路由
2. 在主应用中注册路由
3. 添加相应的测试用例

## 许可证

MIT License