// 工作流执行相关的类型定义
import { 
  AgentNodeType, 
  AgentInput, 
  AgentOutput, 
  WorkflowStatus, 
  ExecutionResult,
  LangGraphState 
} from './index';

// 工作流执行计划（扩展版本）
export interface DetailedWorkflowExecutionPlan {
  workflowId: string;
  executionId: string;
  createdAt: Date;
  steps: DetailedExecutionStep[];
  parallelGroups: ParallelExecutionGroup[];
  dependencies: Map<AgentNodeType, AgentNodeType[]>;
  estimatedDuration: number;
}

// 详细执行步骤
export interface DetailedExecutionStep {
  stepId: string;
  nodeId: AgentNodeType;
  stepType: 'sequential' | 'parallel' | 'conditional';
  dependencies: AgentNodeType[];
  estimatedDuration: number;
  priority: number;
  canSkip: boolean;
}

// 并行执行组
export interface ParallelExecutionGroup {
  groupId: string;
  nodes: AgentNodeType[];
  maxConcurrency: number;
  waitForAll: boolean;
  timeout: number;
}

// 工作流执行器接口
export interface WorkflowExecutor {
  initialize(): Promise<void>;
  createExecutionPlan(workflowId: string): Promise<DetailedWorkflowExecutionPlan>;
  executeWorkflow(plan: DetailedWorkflowExecutionPlan, initialInput: any): Promise<WorkflowExecutionResult>;
  pauseExecution(workflowId: string): Promise<void>;
  resumeExecution(workflowId: string): Promise<void>;
  cancelExecution(workflowId: string): Promise<void>;
  getExecutionStatus(workflowId: string): Promise<WorkflowExecutionStatus>;
}

// 工作流执行结果
export interface WorkflowExecutionResult {
  workflowId: string;
  executionId: string;
  status: 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime: Date;
  totalDuration: number;
  nodeResults: Map<AgentNodeType, AgentOutput>;
  finalResult: any;
  executionSummary: ExecutionSummary;
  errors: ExecutionError[];
}

// 工作流执行状态
export interface WorkflowExecutionStatus {
  workflowId: string;
  executionId: string;
  currentPhase: 'initializing' | 'executing' | 'paused' | 'completing' | 'completed' | 'failed';
  currentNodes: AgentNodeType[];
  completedNodes: AgentNodeType[];
  failedNodes: AgentNodeType[];
  progress: {
    percentage: number;
    completedSteps: number;
    totalSteps: number;
    estimatedTimeRemaining: number;
  };
  startTime: Date;
  lastUpdateTime: Date;
}

// 执行摘要
export interface ExecutionSummary {
  totalNodes: number;
  successfulNodes: number;
  failedNodes: number;
  skippedNodes: number;
  parallelExecutions: number;
  retryAttempts: number;
  performanceMetrics: {
    averageNodeExecutionTime: number;
    totalExecutionTime: number;
    peakMemoryUsage: number;
    totalCpuTime: number;
  };
}

// 执行错误
export interface ExecutionError {
  errorId: string;
  nodeId: AgentNodeType;
  errorType: 'timeout' | 'validation' | 'runtime' | 'dependency' | 'resource';
  message: string;
  stackTrace?: string;
  timestamp: Date;
  retryCount: number;
  isFatal: boolean;
  context: Record<string, any>;
}

// 节点执行器接口
export interface NodeExecutor {
  executeNode(
    nodeId: AgentNodeType, 
    input: AgentInput, 
    context: NodeExecutionContext
  ): Promise<NodeExecutionResult>;
  
  validateNodeInput(nodeId: AgentNodeType, input: AgentInput): Promise<boolean>;
  prepareNodeExecution(nodeId: AgentNodeType, context: NodeExecutionContext): Promise<void>;
  cleanupNodeExecution(nodeId: AgentNodeType, context: NodeExecutionContext): Promise<void>;
}

// 节点执行上下文
export interface NodeExecutionContext {
  workflowId: string;
  executionId: string;
  nodeId: AgentNodeType;
  sandboxPath: string;
  timeout: number;
  maxRetries: number;
  currentRetry: number;
  previousResults: Map<AgentNodeType, AgentOutput>;
  globalState: Record<string, any>;
  executionMetadata: Record<string, any>;
}

// 节点执行结果
export interface NodeExecutionResult {
  nodeId: AgentNodeType;
  executionId: string;
  status: 'success' | 'error' | 'timeout' | 'skipped';
  output?: AgentOutput;
  error?: ExecutionError;
  executionTime: number;
  resourceUsage: {
    memory: number;
    cpu: number;
    diskIO: number;
    networkIO: number;
  };
  metadata: Record<string, any>;
}

// 条件执行器接口
export interface ConditionalExecutor {
  evaluateCondition(
    condition: string, 
    context: any, 
    previousResults: Map<AgentNodeType, AgentOutput>
  ): Promise<boolean>;
  
  getNextNodes(
    currentNode: AgentNodeType, 
    result: AgentOutput, 
    workflowDefinition: any
  ): Promise<AgentNodeType[]>;
}

// 并行执行管理器接口
export interface ParallelExecutionManager {
  executeParallel(
    nodes: AgentNodeType[], 
    inputs: Map<AgentNodeType, AgentInput>,
    maxConcurrency: number
  ): Promise<Map<AgentNodeType, NodeExecutionResult>>;
  
  monitorParallelExecution(executionId: string): Promise<ParallelExecutionStatus>;
  cancelParallelExecution(executionId: string): Promise<void>;
}

// 并行执行状态
export interface ParallelExecutionStatus {
  executionId: string;
  totalNodes: number;
  runningNodes: AgentNodeType[];
  completedNodes: AgentNodeType[];
  failedNodes: AgentNodeType[];
  progress: number;
  startTime: Date;
  estimatedCompletionTime?: Date;
}

// 检查点管理器接口
export interface CheckpointManager {
  createCheckpoint(workflowId: string, state: LangGraphState): Promise<string>;
  restoreFromCheckpoint(workflowId: string, checkpointId: string): Promise<LangGraphState>;
  listCheckpoints(workflowId: string): Promise<CheckpointInfo[]>;
  deleteCheckpoint(checkpointId: string): Promise<void>;
  cleanupOldCheckpoints(olderThanDays: number): Promise<number>;
}

// 检查点信息
export interface CheckpointInfo {
  checkpointId: string;
  workflowId: string;
  createdAt: Date;
  description: string;
  nodeId: AgentNodeType;
  stateSize: number;
  metadata: Record<string, any>;
}

// 执行调度器接口
export interface ExecutionScheduler {
  scheduleExecution(plan: DetailedWorkflowExecutionPlan): Promise<void>;
  getNextExecutableNodes(workflowId: string): Promise<AgentNodeType[]>;
  markNodeCompleted(workflowId: string, nodeId: AgentNodeType, result: AgentOutput): Promise<void>;
  markNodeFailed(workflowId: string, nodeId: AgentNodeType, error: ExecutionError): Promise<void>;
  canExecuteNode(workflowId: string, nodeId: AgentNodeType): Promise<boolean>;
  rescheduleFailedNode(workflowId: string, nodeId: AgentNodeType): Promise<void>;
}