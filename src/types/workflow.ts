// 工作流相关的类型定义
import { AgentNodeType, WorkflowStatus, ExecutionResult, MonitoringEvent } from './index';

// 工作流图定义
export interface WorkflowGraph {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  startNode: AgentNodeType;
  endNode: AgentNodeType;
}

// 工作流节点定义
export interface WorkflowNode {
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  dependencies: AgentNodeType[];
  conditions?: WorkflowCondition[];
  config: NodeConfig;
}

// 工作流边定义
export interface WorkflowEdge {
  from: AgentNodeType;
  to: AgentNodeType;
  condition?: (result: any) => boolean;
  weight?: number;
}

// 节点配置
export interface NodeConfig {
  timeout: number;
  maxRetries: number;
  retryDelay: number;
  skipOnError: boolean;
  saveCheckpoint: boolean;
}

// 工作流条件
export interface WorkflowCondition {
  type: 'success' | 'error' | 'custom';
  condition: (result: any) => boolean;
  action: 'continue' | 'skip' | 'retry' | 'stop';
  description: string;
}

// 工作流执行计划
export interface WorkflowExecutionPlan {
  workflowId: string;
  graph: WorkflowGraph;
  executionOrder: ExecutionStep[];
  parallelGroups: ParallelGroup[];
  estimatedDuration: number;
}

// 执行步骤
export interface ExecutionStep {
  stepId: string;
  nodeId: AgentNodeType;
  dependencies: string[];
  canRunInParallel: boolean;
  estimatedDuration: number;
}

// 并行执行组
export interface ParallelGroup {
  groupId: string;
  nodes: AgentNodeType[];
  maxConcurrency: number;
  waitForAll: boolean;
}

// 工作流引擎接口
export interface WorkflowEngine {
  initialize(): Promise<void>;
  createWorkflow(graph: WorkflowGraph): Promise<string>;
  startWorkflow(workflowId: string, input: any): Promise<void>;
  pauseWorkflow(workflowId: string): Promise<void>;
  resumeWorkflow(workflowId: string): Promise<void>;
  stopWorkflow(workflowId: string): Promise<void>;
  getWorkflowStatus(workflowId: string): Promise<WorkflowStatus>;
  cleanup(): Promise<void>;
}

// 工作流调度器接口
export interface WorkflowScheduler {
  schedule(workflowId: string, plan: WorkflowExecutionPlan): Promise<void>;
  getNextNodes(workflowId: string): Promise<AgentNodeType[]>;
  markNodeComplete(workflowId: string, nodeId: AgentNodeType, result: any): Promise<void>;
  markNodeFailed(workflowId: string, nodeId: AgentNodeType, error: string): Promise<void>;
  canExecuteNode(workflowId: string, nodeId: AgentNodeType): Promise<boolean>;
}

// 工作流状态管理器接口
export interface WorkflowStateManager {
  createState(workflowId: string): Promise<void>;
  updateState(workflowId: string, updates: Partial<WorkflowStatus>): Promise<void>;
  getState(workflowId: string): Promise<WorkflowStatus | null>;
  deleteState(workflowId: string): Promise<void>;
  saveCheckpoint(workflowId: string, nodeId: AgentNodeType, data: any): Promise<void>;
  loadCheckpoint(workflowId: string, checkpointId: string): Promise<any>;
}

// 工作流事件处理器接口
export interface WorkflowEventHandler {
  onWorkflowStart(workflowId: string): Promise<void>;
  onWorkflowComplete(workflowId: string, result: any): Promise<void>;
  onWorkflowError(workflowId: string, error: string): Promise<void>;
  onNodeStart(workflowId: string, nodeId: AgentNodeType): Promise<void>;
  onNodeComplete(workflowId: string, nodeId: AgentNodeType, result: any): Promise<void>;
  onNodeError(workflowId: string, nodeId: AgentNodeType, error: string): Promise<void>;
}

// 工作流监控器接口
export interface WorkflowMonitor {
  startMonitoring(workflowId: string): void;
  stopMonitoring(workflowId: string): void;
  recordEvent(event: MonitoringEvent): void;
  getMetrics(workflowId: string): Promise<WorkflowMetrics>;
  generateReport(workflowId: string): Promise<WorkflowReport>;
}

// 工作流指标
export interface WorkflowMetrics {
  workflowId: string;
  totalExecutionTime: number;
  nodeExecutionTimes: Map<AgentNodeType, number>;
  successRate: number;
  errorRate: number;
  throughput: number;
  resourceUsage: {
    memory: number;
    cpu: number;
    storage: number;
  };
}

// 工作流报告
export interface WorkflowReport {
  workflowId: string;
  summary: {
    status: string;
    startTime: Date;
    endTime?: Date;
    duration: number;
    nodesExecuted: number;
    nodesSucceeded: number;
    nodesFailed: number;
  };
  nodeDetails: Array<{
    nodeId: AgentNodeType;
    status: string;
    executionTime: number;
    input: any;
    output: any;
    error?: string;
  }>;
  timeline: Array<{
    timestamp: Date;
    event: string;
    nodeId?: AgentNodeType;
    details: any;
  }>;
  metrics: WorkflowMetrics;
}