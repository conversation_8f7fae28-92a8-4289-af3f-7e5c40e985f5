// 存储相关的类型定义
import { ExecutionResult, QueryFilter } from './index';

// 数据库操作接口
export interface DatabaseOperations {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  ping(): Promise<boolean>;
}

// MongoDB 存储接口
export interface MongoDBStorage extends DatabaseOperations {
  saveExecutionResult(result: ExecutionResult): Promise<void>;
  getExecutionResult(workflowId: string, nodeId: string): Promise<ExecutionResult | null>;
  queryExecutionResults(filter: QueryFilter): Promise<ExecutionResult[]>;
  deleteExecutionResults(workflowId: string): Promise<number>;
  createIndexes(): Promise<void>;
  getCollectionStats(): Promise<CollectionStats>;
}

// Redis 缓存接口
export interface RedisCache extends DatabaseOperations {
  set(key: string, value: any, ttl?: number): Promise<void>;
  get(key: string): Promise<any>;
  delete(key: string): Promise<boolean>;
  exists(key: string): Promise<boolean>;
  clear(): Promise<void>;
  keys(pattern: string): Promise<string[]>;
  expire(key: string, ttl: number): Promise<boolean>;
}

// 缓存策略接口
export interface CacheStrategy {
  shouldCache(key: string, data: any): boolean;
  generateKey(workflowId: string, nodeId: string): string;
  getTTL(dataType: string): number;
  onCacheHit(key: string): void;
  onCacheMiss(key: string): void;
}

// 数据同步接口
export interface DataSynchronizer {
  syncCacheToDatabase(): Promise<SyncResult>;
  syncDatabaseToCache(): Promise<SyncResult>;
  scheduleSync(intervalMs: number): void;
  stopSync(): void;
  getLastSyncTime(): Date | null;
}

// 同步结果
export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  errors: string[];
  duration: number;
  timestamp: Date;
}

// 集合统计信息
export interface CollectionStats {
  documentCount: number;
  storageSize: number;
  avgDocumentSize: number;
  indexCount: number;
  totalIndexSize: number;
}

// 存储配置
export interface StorageConfig {
  mongodb: {
    uri: string;
    database: string;
    collections: {
      executionResults: string;
      workflowStatus: string;
      checkpoints: string;
      monitoring: string;
    };
    options: {
      maxPoolSize: number;
      serverSelectionTimeoutMS: number;
      socketTimeoutMS: number;
      retryWrites: boolean;
    };
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    database: number;
    keyPrefix: string;
    defaultTTL: number;
    maxMemoryPolicy: string;
  };
  cache: {
    enabled: boolean;
    strategy: 'write-through' | 'write-back' | 'write-around';
    maxSize: number;
    syncInterval: number;
  };
}

// 数据仓库接口
export interface DataRepository<T> {
  create(item: T): Promise<T>;
  findById(id: string): Promise<T | null>;
  findMany(filter: any): Promise<T[]>;
  update(id: string, updates: Partial<T>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
  count(filter?: any): Promise<number>;
}

// 执行结果仓库接口
export interface ExecutionResultRepository extends DataRepository<ExecutionResult> {
  findByWorkflowId(workflowId: string): Promise<ExecutionResult[]>;
  findByNodeId(nodeId: string): Promise<ExecutionResult[]>;
  findByDateRange(startDate: Date, endDate: Date): Promise<ExecutionResult[]>;
  findByStatus(status: 'success' | 'error' | 'skipped'): Promise<ExecutionResult[]>;
  getLatestResults(limit: number): Promise<ExecutionResult[]>;
  deleteOldResults(olderThanDays: number): Promise<number>;
}

// 备份和恢复接口
export interface BackupManager {
  createBackup(backupName: string): Promise<BackupInfo>;
  restoreBackup(backupName: string): Promise<RestoreResult>;
  listBackups(): Promise<BackupInfo[]>;
  deleteBackup(backupName: string): Promise<boolean>;
  scheduleBackup(cronExpression: string): void;
  stopScheduledBackup(): void;
}

// 备份信息
export interface BackupInfo {
  name: string;
  createdAt: Date;
  size: number;
  collections: string[];
  checksum: string;
  metadata: Record<string, any>;
}

// 恢复结果
export interface RestoreResult {
  success: boolean;
  restoredCollections: string[];
  failedCollections: string[];
  duration: number;
  errors: string[];
}