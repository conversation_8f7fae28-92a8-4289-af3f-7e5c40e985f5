// 工作流输入接口
export interface WorkflowInput {
  id?: string;
  parameters?: Record<string, any>;
  metadata?: Record<string, any>;
  initialPrompt?: string;
  config?: WorkflowConfig;
}

// 工作流配置接口
export interface WorkflowConfig {
  enableMonitoring: boolean;
  saveCheckpoints: boolean;
  maxExecutionTime: number;
  retryPolicy: {
    maxRetries: number;
    retryDelay: number;
  };
  parallelExecution: {
    enabled: boolean;
    maxConcurrency: number;
  };
}

// 工作流结果接口
export interface WorkflowResult {
  workflowId: string;
  status: WorkflowStatus;
  results: Record<string, any>;
  executionTime: number;
  error?: string;
}

// 工作流状态类型
export type WorkflowStatusType = 'running' | 'completed' | 'failed' | 'stopped';

// 工作流状态接口
export interface WorkflowStatus {
  workflowId: string;
  status: WorkflowStatusType;
  currentNode?: string;
  progress: number;
  startTime: Date;
  endTime?: Date;
  nodes: NodeStatus[];
  executionPath: AgentNodeType[];
  checkpoints: WorkflowCheckpoint[];
}

// 工作流检查点
export interface WorkflowCheckpoint {
  checkpointId: string;
  workflowId: string;
  timestamp: Date;
  completedNodes: AgentNodeType[];
  currentState: Record<string, any>;
  nextNodes: AgentNodeType[];
}

// 条件分支配置
export interface ConditionalBranch {
  condition: (result: any) => boolean;
  truePath: AgentNodeType[];
  falsePath: AgentNodeType[];
  description: string;
}

// 节点状态接口
export interface NodeStatus {
  nodeId: AgentNodeType;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: Date;
  endTime?: Date;
  executionTime?: number;
  output?: any;
  errorMessage?: string;
  retryCount: number;
  progress: number;
  dependencies: AgentNodeType[];
  dependenciesCompleted: AgentNodeType[];
}

// 执行结果接口
export interface ExecutionResult {
  resultId: string;
  workflowId: string;
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  startTime: Date;
  endTime: Date;
  executionTime: number;
  input: any;
  output: any;
  status: 'success' | 'error' | 'skipped';
  errorMessage?: string;
  executionPath: string;
  retryCount: number;
  sandboxPath: string;
  resourceUsage: {
    memory: number;
    cpu: number;
    diskIO: number;
  };
  metadata: Record<string, any>;
}

// 智能体节点类型
export type AgentNodeType = 'agent_a' | 'agent_b' | 'agent_c' | 'agent_d';

// 智能体节点配置
export interface AgentNodeConfig {
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  sandboxPath: string;
  maxRetries: number;
  timeout: number;
  dependencies: AgentNodeType[];
}

// LLM 配置
export interface LLMConfig {
  provider: string;
  model: string;
  apiKey: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

// 沙箱环境配置
export interface SandboxConfig {
  basePath: string;
  nodeId: string;
  workflowId: string;
  permissions: {
    read: boolean;
    write: boolean;
    execute: boolean;
  };
  resourceLimits: {
    maxMemory: number;
    maxExecutionTime: number;
    maxFileSize: number;
  };
}

// 执行报告接口
export interface ExecutionReport {
  workflowId: string;
  summary: {
    totalNodes: number;
    completedNodes: number;
    failedNodes: number;
    totalExecutionTime: number;
  };
  nodeResults: ExecutionResult[];
  timeline: Array<{
    timestamp: Date;
    nodeId: string;
    event: string;
    details?: any;
  }>;
}

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 查询过滤器接口
export interface QueryFilter {
  workflowId?: string;
  nodeType?: 'llm' | 'function';
  status?: 'success' | 'error' | 'skipped';
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

// 智能体相关接口
export interface AgentInput {
  data: any;
  context: Record<string, any>;
  sandboxPath: string;
  workflowId: string;
  nodeId: AgentNodeType;
  previousResults?: Record<AgentNodeType, any>;
  executionId: string;
  timestamp: Date;
  config?: Record<string, any>;
}

export interface AgentOutput {
  result: any;
  metadata: Record<string, any>;
  executionTime: number;
  status: 'success' | 'error';
  errorMessage?: string;
  nodeId: AgentNodeType;
  timestamp: Date;
  resourceUsage?: {
    memory: number;
    cpu: number;
    diskIO: number;
  };
}

// 工作流管理器接口
export interface WorkflowManager {
  initialize(): Promise<void>;
  createWorkflow(input: WorkflowInput): Promise<string>;
  executeWorkflow(workflowId: string): Promise<WorkflowResult>;
  pauseWorkflow(workflowId: string): Promise<void>;
  resumeWorkflow(workflowId: string): Promise<void>;
  stopWorkflow(workflowId: string): Promise<void>;
  getWorkflowStatus(workflowId: string): Promise<WorkflowStatus>;
  listActiveWorkflows(): Promise<string[]>;
  cleanup(): Promise<void>;
}

// 工作流执行引擎接口
export interface WorkflowExecutionEngine {
  executeNode(nodeId: AgentNodeType, input: AgentInput): Promise<AgentOutput>;
  executeParallelNodes(nodeIds: AgentNodeType[], inputs: AgentInput[]): Promise<Map<AgentNodeType, AgentOutput>>;
  checkNodeDependencies(nodeId: AgentNodeType, completedNodes: AgentNodeType[]): boolean;
  getNextExecutableNodes(workflowId: string): Promise<AgentNodeType[]>;
  handleNodeError(nodeId: AgentNodeType, error: Error): Promise<void>;
}

// 基础智能体接口
export interface BaseAgent {
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';

  initialize(): Promise<void>;
  execute(input: AgentInput): Promise<AgentOutput>;
  cleanup(): Promise<void>;
  getStatus(): AgentStatus;
  getSandboxPath(): string;
}

// 智能体状态接口
export interface AgentStatus {
  nodeId: AgentNodeType;
  isInitialized: boolean;
  isExecuting: boolean;
  lastExecutionTime?: Date;
  errorCount: number;
  successCount: number;
  currentLoad: number;
}

// LLM 智能体接口 (NodeA, NodeB, NodeD)
export interface LLMAgent extends BaseAgent {
  nodeType: 'llm';
  llmConfig: LLMConfig;

  callLLM(prompt: string, context?: Record<string, any>): Promise<string>;
  retry(input: AgentInput, maxRetries: number): Promise<AgentOutput>;
  validateOutput(output: any): boolean;
  getModel(): string;
}

// 函数智能体接口 (NodeC)
export interface FunctionAgent extends BaseAgent {
  nodeType: 'function';

  executeFunction(codeA: string, suffix: string): Promise<string>;
  validateFunctionInput(input: any): boolean;
}

// 存储服务接口
export interface StorageService {
  initialize(): Promise<void>;
  saveExecutionResult(result: ExecutionResult): Promise<void>;
  saveWorkflowStatus(status: WorkflowStatus): Promise<void>;
  queryResults(filter: QueryFilter): Promise<ExecutionResult[]>;
  getWorkflowStatus(workflowId: string): Promise<WorkflowStatus | null>;
  initializeCache(): Promise<void>;
  getCachedResult(key: string): Promise<ExecutionResult | null>;
  setCachedResult(key: string, result: ExecutionResult, ttl?: number): Promise<void>;
  syncCacheToDatabase(): Promise<void>;
  clearCache(): Promise<void>;
  getStorageStats(): Promise<StorageStats>;
  cleanup(): Promise<void>;
}

// 存储统计信息
export interface StorageStats {
  totalExecutionResults: number;
  totalWorkflows: number;
  cacheHitRate: number;
  databaseSize: number;
  cacheSize: number;
  lastSyncTime: Date;
}

// 监控服务接口
export interface MonitoringService {
  initialize(): Promise<void>;
  startMonitoring(workflowId: string): void;
  stopMonitoring(workflowId: string): void;
  updateNodeStatus(workflowId: string, nodeId: AgentNodeType, status: NodeStatus): void;
  recordEvent(event: MonitoringEvent): void;
  sendAlert(alert: Alert): void;
  generateReport(workflowId: string): Promise<ExecutionReport>;
  getMetrics(workflowId: string): Promise<any>;
  getSystemHealth(): Promise<SystemHealth>;
  cleanup(): Promise<void>;
}

// 系统健康状态
export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  components: {
    database: ComponentHealth;
    cache: ComponentHealth;
    agents: ComponentHealth;
    monitoring: ComponentHealth;
  };
  timestamp: Date;
  uptime: number;
}

// 组件健康状态
export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  message?: string;
  lastCheck: Date;
  responseTime: number;
  errorRate: number;
}

// 错误恢复接口
export interface ErrorRecovery {
  retryNode(nodeId: string, maxRetries: number): Promise<boolean>;
  skipNode(nodeId: string, reason: string): Promise<void>;
  rollbackToCheckpoint(checkpointId: string): Promise<void>;
  manualIntervention(workflowId: string): Promise<void>;
}

// 告警接口
export interface Alert {
  type: 'error' | 'warning' | 'info';
  message: string;
  workflowId: string;
  nodeId?: string;
  timestamp: Date;
}

// 配置接口
export interface AppConfig {
  port: number;
  mongodb: {
    uri: string;
    dbName: string;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
  };
  llm: {
    provider: string;
    apiKey: string;
    model: string;
  };
  sandbox: {
    basePath: string;
    maxExecutionTime: number;
  };
}

// Agent A 特定接口 (第一个 LLM 智能体)
export interface AgentAInput extends AgentInput {
  initialPrompt: string;
  parameters: Record<string, any>;
}

export interface AgentAOutput extends AgentOutput {
  codeA: string;
  analysisResult: any;
  processingSteps: string[];
}

// Agent A 实现接口
export interface AgentAInterface extends LLMAgent {
  nodeId: 'agent_a';

  processInitialInput(input: AgentAInput): Promise<AgentAOutput>;
  generateCodeA(prompt: string): Promise<string>;
}

// Agent B 特定接口 (并行 LLM 智能体，支持多次调用)
export interface AgentBInput extends AgentInput {
  prompts: string[];
  multiCallConfig: {
    maxCalls: number;
    callInterval: number;
    aggregationStrategy: 'concat' | 'merge' | 'select_best';
  };
}

export interface AgentBOutput extends AgentOutput {
  multipleResults: any[];
  aggregatedResult: any;
  callCount: number;
  callDetails: Array<{
    callIndex: number;
    prompt: string;
    result: any;
    executionTime: number;
  }>;
}

// Agent B 实现接口
export interface AgentBInterface extends LLMAgent {
  nodeId: 'agent_b';

  executeMultipleCalls(input: AgentBInput): Promise<AgentBOutput>;
  aggregateMultipleResults(results: any[], strategy: string): Promise<any>;
}

// Agent C 特定接口 (函数执行智能体)
export interface AgentCInput extends AgentInput {
  codeA: string; // 来自 Agent A 的结果
  functionSuffix: string; // "xxxxdx"
  executionMode: 'safe' | 'normal';
}

export interface AgentCOutput extends AgentOutput {
  functionResult: string;
  executedCode: string;
  executionLog: string[];
  securityChecks: {
    passed: boolean;
    warnings: string[];
  };
}

// Agent C 实现接口
export interface AgentCInterface extends FunctionAgent {
  nodeId: 'agent_c';

  executeFunction(codeA: string, suffix: string): Promise<string>;
  validateCodeSafety(code: string): Promise<boolean>;
  processAgentAResult(agentAOutput: AgentAOutput): Promise<AgentCOutput>;
}

// Agent D 特定接口 (汇聚 LLM 智能体)
export interface AgentDInput extends AgentInput {
  agentBResult: AgentBOutput;
  agentCResult: AgentCOutput;
  aggregationPrompt: string;
  finalOutputFormat: 'json' | 'text' | 'structured';
}

export interface AgentDOutput extends AgentOutput {
  finalResult: any;
  summary: string;
  aggregatedData: Record<string, any>;
  consolidationReport: {
    sourcesUsed: AgentNodeType[];
    conflictsResolved: string[];
    confidenceScore: number;
  };
}

// Agent D 实现接口
export interface AgentDInterface extends LLMAgent {
  nodeId: 'agent_d';

  aggregateResults(input: AgentDInput): Promise<AgentDOutput>;
  consolidateMultipleInputs(agentBResult: AgentBOutput, agentCResult: AgentCOutput): Promise<any>;
  generateFinalSummary(aggregatedData: any): Promise<string>;
}

// 工作流执行上下文
export interface WorkflowExecutionContext {
  workflowId: string;
  startTime: Date;
  currentNode?: AgentNodeType;
  nodeResults: Map<AgentNodeType, AgentOutput>;
  globalState: Record<string, any>;
  sandboxPaths: Map<AgentNodeType, string>;
  errorHistory: Array<{
    nodeId: AgentNodeType;
    error: string;
    timestamp: Date;
    retryCount: number;
  }>;
}

// LangGraph 状态接口
export interface LangGraphState {
  workflowId: string;
  currentStep: AgentNodeType | '__start__' | '__end__';
  agentResults: {
    agent_a?: AgentAOutput;
    agent_b?: AgentBOutput;
    agent_c?: AgentCOutput;
    agent_d?: AgentDOutput;
  };
  executionPath: Array<AgentNodeType | '__start__' | '__end__'>;
  parallelExecutions: Map<AgentNodeType, boolean>;
  globalContext: Record<string, any>;
  metadata: Record<string, any>;
  checkpoints: Array<{
    step: string;
    timestamp: Date;
    state: any;
  }>;
}

// LangGraph 工作流定义
export interface LangGraphWorkflowDefinition {
  nodes: Map<AgentNodeType, LangGraphNode>;
  edges: LangGraphEdge[];
  conditionalEdges: LangGraphConditionalEdge[];
  entryPoint: '__start__';
  exitPoint: '__end__';
}

// LangGraph 节点定义
export interface LangGraphNode {
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  agent: BaseAgent;
  config: {
    timeout: number;
    retries: number;
    parallel: boolean;
  };
}

// LangGraph 边定义
export interface LangGraphEdge {
  from: AgentNodeType | '__start__';
  to: AgentNodeType | '__end__';
  condition?: (state: LangGraphState) => boolean;
}

// LangGraph 条件边定义
export interface LangGraphConditionalEdge {
  from: AgentNodeType;
  condition: (state: LangGraphState) => AgentNodeType | '__end__';
  mapping: Record<string, AgentNodeType | '__end__'>;
}

// 数据库连接配置
export interface DatabaseConfig {
  mongodb: {
    uri: string;
    dbName: string;
    collections: {
      executionResults: string;
      workflowStatus: string;
      checkpoints: string;
    };
    options: {
      maxPoolSize: number;
      serverSelectionTimeoutMS: number;
      socketTimeoutMS: number;
    };
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
    ttl: number;
  };
}

// API 请求和响应类型
export interface StartWorkflowRequest {
  input: WorkflowInput;
  config?: {
    enableMonitoring: boolean;
    saveCheckpoints: boolean;
    maxExecutionTime: number;
  };
}

export interface StopWorkflowRequest {
  workflowId: string;
  reason?: string;
  saveState?: boolean;
}

export interface QueryResultsRequest {
  filter: QueryFilter;
  pagination: {
    page: number;
    limit: number;
  };
  sortBy?: {
    field: string;
    order: 'asc' | 'desc';
  };
}

// 监控事件类型
export interface MonitoringEvent {
  eventId: string;
  workflowId: string;
  nodeId?: AgentNodeType;
  eventType: 'node_start' | 'node_complete' | 'node_error' | 'workflow_start' | 'workflow_complete' | 'workflow_error';
  timestamp: Date;
  data: any;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

// 安全策略配置
export interface SecurityPolicy {
  sandboxIsolation: {
    enabled: boolean;
    allowedPaths: string[];
    blockedPaths: string[];
    maxFileOperations: number;
  };
  apiSecurity: {
    rateLimiting: {
      windowMs: number;
      maxRequests: number;
    };
    inputValidation: boolean;
    outputSanitization: boolean;
  };
  dataProtection: {
    encryptSensitiveData: boolean;
    auditLogging: boolean;
    dataRetentionDays: number;
  };
}

// 沙箱管理器接口
export interface SandboxManager {
  createSandbox(workflowId: string, nodeId: AgentNodeType): Promise<string>;
  initializeSandbox(sandboxPath: string, config: SandboxConfig): Promise<void>;
  cleanupSandbox(sandboxPath: string): Promise<void>;
  validateSandboxAccess(sandboxPath: string, operation: 'read' | 'write' | 'execute'): boolean;
  getSandboxStats(sandboxPath: string): Promise<SandboxStats>;
}

// 沙箱统计信息
export interface SandboxStats {
  path: string;
  size: number;
  fileCount: number;
  createdAt: Date;
  lastAccessed: Date;
  permissions: {
    read: boolean;
    write: boolean;
    execute: boolean;
  };
}

// 错误处理和恢复接口
export interface ErrorHandler {
  handleNodeError(nodeId: AgentNodeType, error: Error, context: any): Promise<ErrorHandlingResult>;
  handleWorkflowError(workflowId: string, error: Error): Promise<void>;
  retryNode(nodeId: AgentNodeType, input: AgentInput, maxRetries: number): Promise<AgentOutput>;
  skipNode(nodeId: AgentNodeType, reason: string): Promise<void>;
  rollbackWorkflow(workflowId: string, checkpointId: string): Promise<void>;
}

// 错误处理结果
export interface ErrorHandlingResult {
  action: 'retry' | 'skip' | 'fail' | 'rollback';
  retryCount: number;
  maxRetries: number;
  nextAction?: string;
  metadata: Record<string, any>;
}

// 工作流验证器接口
export interface WorkflowValidator {
  validateWorkflowInput(input: WorkflowInput): ValidationResult;
  validateAgentInput(nodeId: AgentNodeType, input: AgentInput): ValidationResult;
  validateAgentOutput(nodeId: AgentNodeType, output: AgentOutput): ValidationResult;
  validateWorkflowDefinition(definition: LangGraphWorkflowDefinition): ValidationResult;
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// 性能监控接口
export interface PerformanceMonitor {
  startProfiling(workflowId: string): void;
  stopProfiling(workflowId: string): PerformanceProfile;
  recordMetric(metric: PerformanceMetric): void;
  getPerformanceReport(workflowId: string): Promise<PerformanceReport>;
}

// 性能配置文件
export interface PerformanceProfile {
  workflowId: string;
  totalExecutionTime: number;
  nodeExecutionTimes: Map<AgentNodeType, number>;
  memoryUsage: number[];
  cpuUsage: number[];
  ioOperations: number;
}

// 性能指标
export interface PerformanceMetric {
  workflowId: string;
  nodeId?: AgentNodeType;
  metricType: 'execution_time' | 'memory_usage' | 'cpu_usage' | 'io_operations';
  value: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// 性能报告
export interface PerformanceReport {
  workflowId: string;
  generatedAt: Date;
  summary: {
    totalExecutionTime: number;
    averageNodeExecutionTime: number;
    peakMemoryUsage: number;
    averageCpuUsage: number;
    totalIoOperations: number;
  };
  nodePerformance: Map<AgentNodeType, {
    executionTime: number;
    memoryUsage: number;
    cpuUsage: number;
    ioOperations: number;
  }>;
  bottlenecks: string[];
  recommendations: string[];
}

// 导出其他类型定义文件
export * from './agents';
export * from './workflow';
export * from './storage';
export * from './monitoring';
export * from './execution';