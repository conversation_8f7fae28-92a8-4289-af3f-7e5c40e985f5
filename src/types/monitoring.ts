// 监控相关的类型定义
import { AgentNodeType, Alert, MonitoringEvent } from './index';

// 监控服务接口
export interface MonitoringService {
  initialize(): Promise<void>;
  startMonitoring(workflowId: string): void;
  stopMonitoring(workflowId: string): void;
  recordEvent(event: MonitoringEvent): void;
  sendAlert(alert: Alert): void;
  getMetrics(workflowId: string): Promise<WorkflowMetrics>;
  generateReport(workflowId: string): Promise<MonitoringReport>;
  cleanup(): Promise<void>;
}

// 指标收集器接口
export interface MetricsCollector {
  collectSystemMetrics(): Promise<SystemMetrics>;
  collectWorkflowMetrics(workflowId: string): Promise<WorkflowMetrics>;
  collectNodeMetrics(nodeId: AgentNodeType): Promise<NodeMetrics>;
  startCollection(intervalMs: number): void;
  stopCollection(): void;
}

// 系统指标
export interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
}

// 导入工作流指标（避免重复定义）
import { WorkflowMetrics } from './workflow';

// 节点指标
export interface NodeMetrics {
  nodeId: AgentNodeType;
  timestamp: Date;
  executionCount: number;
  successCount: number;
  errorCount: number;
  averageExecutionTime: number;
  lastExecutionTime?: Date;
  resourceUsage: ResourceUsage;
}

// 资源使用情况
export interface ResourceUsage {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

// 性能指标
export interface PerformanceMetrics {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  errorRate: number;
  requestsPerSecond: number;
}

// 告警管理器接口
export interface AlertManager {
  createAlert(alert: Alert): Promise<void>;
  getAlerts(workflowId?: string): Promise<Alert[]>;
  acknowledgeAlert(alertId: string): Promise<void>;
  resolveAlert(alertId: string): Promise<void>;
  configureAlertRules(rules: AlertRule[]): void;
  getAlertRules(): AlertRule[];
}

// 告警规则
export interface AlertRule {
  ruleId: string;
  name: string;
  description: string;
  condition: AlertCondition;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownPeriod: number;
  actions: AlertAction[];
}

// 告警条件
export interface AlertCondition {
  metric: string;
  operator: '>' | '<' | '=' | '>=' | '<=' | '!=';
  threshold: number;
  duration: number;
  aggregation?: 'avg' | 'sum' | 'min' | 'max' | 'count';
}

// 告警动作
export interface AlertAction {
  type: 'email' | 'webhook' | 'log' | 'sms';
  config: Record<string, any>;
  enabled: boolean;
}

// 事件存储接口
export interface EventStore {
  storeEvent(event: MonitoringEvent): Promise<void>;
  getEvents(filter: EventFilter): Promise<MonitoringEvent[]>;
  deleteOldEvents(olderThanDays: number): Promise<number>;
  getEventCount(filter?: EventFilter): Promise<number>;
}

// 事件过滤器
export interface EventFilter {
  workflowId?: string;
  nodeId?: AgentNodeType;
  eventType?: string;
  severity?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

// 监控报告
export interface MonitoringReport {
  workflowId: string;
  reportId: string;
  generatedAt: Date;
  period: {
    startDate: Date;
    endDate: Date;
  };
  summary: {
    totalWorkflows: number;
    successfulWorkflows: number;
    failedWorkflows: number;
    averageExecutionTime: number;
    totalEvents: number;
    alertsGenerated: number;
  };
  metrics: {
    system: SystemMetrics[];
    workflows: WorkflowMetrics[];
    nodes: NodeMetrics[];
  };
  alerts: Alert[];
  events: MonitoringEvent[];
  recommendations: string[];
}

// 健康检查接口
export interface HealthChecker {
  checkHealth(): Promise<HealthStatus>;
  checkComponentHealth(component: string): Promise<ComponentHealth>;
  registerHealthCheck(name: string, check: () => Promise<boolean>): void;
  unregisterHealthCheck(name: string): void;
}

// 健康状态
export interface HealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  components: Map<string, ComponentHealth>;
  uptime: number;
}

// 组件健康状态
export interface ComponentHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  message?: string;
  lastCheck: Date;
  responseTime: number;
}

// 监控配置
export interface MonitoringConfig {
  enabled: boolean;
  metricsCollection: {
    interval: number;
    retention: number;
    batchSize: number;
  };
  alerting: {
    enabled: boolean;
    defaultRules: AlertRule[];
    channels: {
      email: {
        enabled: boolean;
        smtp: any;
      };
      webhook: {
        enabled: boolean;
        url: string;
      };
    };
  };
  reporting: {
    enabled: boolean;
    schedule: string;
    recipients: string[];
  };
  healthCheck: {
    enabled: boolean;
    interval: number;
    timeout: number;
  };
}