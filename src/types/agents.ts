// 智能体相关的类型定义
import { 
  AgentInput, 
  AgentOutput, 
  AgentNodeType, 
  LLMConfig, 
  SandboxConfig,
  BaseAgent,
  AgentStatus,
  LLMAgent,
  FunctionAgent,
  AgentAInput,
  AgentAOutput,
  AgentAInterface,
  AgentBInput,
  AgentBOutput,
  AgentBInterface,
  AgentCInput,
  AgentCOutput,
  AgentCInterface,
  AgentDInput,
  AgentDOutput,
  AgentDInterface
} from './index';

// 智能体配置接口
export interface AgentConfiguration {
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  config: {
    timeout: number;
    maxRetries: number;
    retryDelay: number;
    enableMonitoring: boolean;
    sandboxConfig: SandboxConfig;
    llmConfig?: LLMConfig;
  };
}

// 智能体执行上下文
export interface AgentExecutionContext {
  workflowId: string;
  nodeId: AgentNodeType;
  executionId: string;
  startTime: Date;
  sandboxPath: string;
  previousResults: Map<AgentNodeType, AgentOutput>;
  globalContext: Record<string, any>;
  retryCount: number;
  maxRetries: number;
}

// 智能体性能指标
export interface AgentPerformanceMetrics {
  nodeId: AgentNodeType;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecutionTime: Date;
  resourceUsage: {
    averageMemory: number;
    peakMemory: number;
    averageCpu: number;
    peakCpu: number;
  };
}

// 智能体工厂接口
export interface AgentFactory {
  createAgent(nodeId: AgentNodeType, config: any): Promise<BaseAgent>;
  getAgent(nodeId: AgentNodeType): BaseAgent | null;
  destroyAgent(nodeId: AgentNodeType): Promise<void>;
  listAgents(): AgentNodeType[];
}

// 智能体注册表接口
export interface AgentRegistry {
  register(agent: BaseAgent): void;
  unregister(nodeId: AgentNodeType): void;
  get(nodeId: AgentNodeType): BaseAgent | null;
  getAll(): Map<AgentNodeType, BaseAgent>;
  clear(): void;
}

// 智能体执行器接口
export interface AgentExecutor {
  executeAgent(nodeId: AgentNodeType, input: AgentInput): Promise<AgentOutput>;
  executeParallel(nodeIds: AgentNodeType[], inputs: AgentInput[]): Promise<AgentOutput[]>;
  executeSequential(nodeIds: AgentNodeType[], input: AgentInput): Promise<AgentOutput[]>;
}