# 核心数据模型和接口定义

本文档描述了 LangGraph 多智能体系统 MVP 的核心数据模型和接口定义。

## 文件结构

```
src/types/
├── index.ts          # 主要类型定义和接口
├── agents.ts         # 智能体相关类型定义
├── workflow.ts       # 工作流相关类型定义
├── storage.ts        # 存储相关类型定义
├── monitoring.ts     # 监控相关类型定义
├── execution.ts      # 执行相关类型定义
└── README.md         # 本文档
```

## 核心接口概览

### 1. 工作流相关接口

#### WorkflowInput
工作流输入接口，包含工作流的基本参数和配置。

```typescript
interface WorkflowInput {
  id?: string;
  parameters?: Record<string, any>;
  metadata?: Record<string, any>;
  initialPrompt?: string;
  config?: WorkflowConfig;
}
```

#### WorkflowStatus
工作流状态接口，跟踪工作流的执行状态和进度。

```typescript
interface WorkflowStatus {
  workflowId: string;
  status: WorkflowStatusType;
  currentNode?: string;
  progress: number;
  startTime: Date;
  endTime?: Date;
  nodes: NodeStatus[];
  executionPath: AgentNodeType[];
  checkpoints: WorkflowCheckpoint[];
}
```

#### ExecutionResult
执行结果接口，记录每个节点的执行详情。

```typescript
interface ExecutionResult {
  resultId: string;
  workflowId: string;
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  startTime: Date;
  endTime: Date;
  executionTime: number;
  input: any;
  output: any;
  status: 'success' | 'error' | 'skipped';
  errorMessage?: string;
  executionPath: string;
  retryCount: number;
  sandboxPath: string;
  resourceUsage: ResourceUsage;
  metadata: Record<string, any>;
}
```

### 2. 智能体相关接口

#### BaseAgent
所有智能体的基础接口。

```typescript
interface BaseAgent {
  nodeId: AgentNodeType;
  nodeType: 'llm' | 'function';
  
  initialize(): Promise<void>;
  execute(input: AgentInput): Promise<AgentOutput>;
  cleanup(): Promise<void>;
  getStatus(): AgentStatus;
  getSandboxPath(): string;
}
```

#### LLMAgent
LLM 智能体接口（Agent A、B、D）。

```typescript
interface LLMAgent extends BaseAgent {
  nodeType: 'llm';
  llmConfig: LLMConfig;
  
  callLLM(prompt: string, context?: Record<string, any>): Promise<string>;
  retry(input: AgentInput, maxRetries: number): Promise<AgentOutput>;
  validateOutput(output: any): boolean;
  getModel(): string;
}
```

#### FunctionAgent
函数智能体接口（Agent C）。

```typescript
interface FunctionAgent extends BaseAgent {
  nodeType: 'function';
  
  executeFunction(codeA: string, suffix: string): Promise<string>;
  validateFunctionInput(input: any): boolean;
}
```

### 3. 特定智能体接口

#### Agent A 接口
第一个 LLM 智能体，负责初始分析和代码生成。

```typescript
interface AgentAInterface extends LLMAgent {
  nodeId: 'agent_a';
  
  processInitialInput(input: AgentAInput): Promise<AgentAOutput>;
  generateCodeA(prompt: string): Promise<string>;
}
```

#### Agent B 接口
并行 LLM 智能体，支持多次调用。

```typescript
interface AgentBInterface extends LLMAgent {
  nodeId: 'agent_b';
  
  executeMultipleCalls(input: AgentBInput): Promise<AgentBOutput>;
  aggregateMultipleResults(results: any[], strategy: string): Promise<any>;
}
```

#### Agent C 接口
函数执行智能体，处理 Agent A 的结果。

```typescript
interface AgentCInterface extends FunctionAgent {
  nodeId: 'agent_c';
  
  executeFunction(codeA: string, suffix: string): Promise<string>;
  validateCodeSafety(code: string): Promise<boolean>;
  processAgentAResult(agentAOutput: AgentAOutput): Promise<AgentCOutput>;
}
```

#### Agent D 接口
汇聚 LLM 智能体，整合所有前置智能体的结果。

```typescript
interface AgentDInterface extends LLMAgent {
  nodeId: 'agent_d';
  
  aggregateResults(input: AgentDInput): Promise<AgentDOutput>;
  consolidateMultipleInputs(agentBResult: AgentBOutput, agentCResult: AgentCOutput): Promise<any>;
  generateFinalSummary(aggregatedData: any): Promise<string>;
}
```

### 4. 服务接口

#### WorkflowManager
工作流管理器接口，负责工作流的生命周期管理。

```typescript
interface WorkflowManager {
  initialize(): Promise<void>;
  createWorkflow(input: WorkflowInput): Promise<string>;
  executeWorkflow(workflowId: string): Promise<WorkflowResult>;
  pauseWorkflow(workflowId: string): Promise<void>;
  resumeWorkflow(workflowId: string): Promise<void>;
  stopWorkflow(workflowId: string): Promise<void>;
  getWorkflowStatus(workflowId: string): Promise<WorkflowStatus>;
  listActiveWorkflows(): Promise<string[]>;
  cleanup(): Promise<void>;
}
```

#### StorageService
存储服务接口，负责数据持久化和缓存。

```typescript
interface StorageService {
  initialize(): Promise<void>;
  saveExecutionResult(result: ExecutionResult): Promise<void>;
  saveWorkflowStatus(status: WorkflowStatus): Promise<void>;
  queryResults(filter: QueryFilter): Promise<ExecutionResult[]>;
  getWorkflowStatus(workflowId: string): Promise<WorkflowStatus | null>;
  initializeCache(): Promise<void>;
  getCachedResult(key: string): Promise<ExecutionResult | null>;
  setCachedResult(key: string, result: ExecutionResult, ttl?: number): Promise<void>;
  syncCacheToDatabase(): Promise<void>;
  clearCache(): Promise<void>;
  getStorageStats(): Promise<StorageStats>;
  cleanup(): Promise<void>;
}
```

#### MonitoringService
监控服务接口，负责实时监控和告警。

```typescript
interface MonitoringService {
  initialize(): Promise<void>;
  startMonitoring(workflowId: string): void;
  stopMonitoring(workflowId: string): void;
  updateNodeStatus(workflowId: string, nodeId: AgentNodeType, status: NodeStatus): void;
  recordEvent(event: MonitoringEvent): void;
  sendAlert(alert: Alert): void;
  generateReport(workflowId: string): Promise<ExecutionReport>;
  getMetrics(workflowId: string): Promise<any>;
  getSystemHealth(): Promise<SystemHealth>;
  cleanup(): Promise<void>;
}
```

### 5. LangGraph 集成接口

#### LangGraphState
LangGraph 状态管理接口。

```typescript
interface LangGraphState {
  workflowId: string;
  currentStep: AgentNodeType | '__start__' | '__end__';
  agentResults: {
    agent_a?: AgentAOutput;
    agent_b?: AgentBOutput;
    agent_c?: AgentCOutput;
    agent_d?: AgentDOutput;
  };
  executionPath: Array<AgentNodeType | '__start__' | '__end__'>;
  parallelExecutions: Map<AgentNodeType, boolean>;
  globalContext: Record<string, any>;
  metadata: Record<string, any>;
  checkpoints: Array<{
    step: string;
    timestamp: Date;
    state: any;
  }>;
}
```

#### LangGraphWorkflowDefinition
LangGraph 工作流定义接口。

```typescript
interface LangGraphWorkflowDefinition {
  nodes: Map<AgentNodeType, LangGraphNode>;
  edges: LangGraphEdge[];
  conditionalEdges: LangGraphConditionalEdge[];
  entryPoint: '__start__';
  exitPoint: '__end__';
}
```

## 工作流执行流程

根据设计文档，工作流执行流程如下：

1. **__start__**: 工作流入口点
2. **并行执行阶段**: agent_a 和 agent_b 同时执行
3. **顺序执行阶段**: agent_c 等待 agent_a 完成后执行
4. **汇聚阶段**: agent_d 等待 agent_b 和 agent_c 完成后执行
5. **__end__**: 工作流结束点

## 类型安全特性

- 所有接口都使用 TypeScript 严格类型检查
- 智能体节点类型使用联合类型 `AgentNodeType = 'agent_a' | 'agent_b' | 'agent_c' | 'agent_d'`
- 状态类型使用字面量类型确保类型安全
- 所有异步操作都有明确的 Promise 返回类型
- 资源使用情况和性能指标都有详细的类型定义

## 扩展性设计

- 接口设计支持未来添加更多智能体节点
- 配置接口支持灵活的参数配置
- 监控和存储接口支持多种实现方式
- 错误处理和恢复机制具有良好的扩展性

## 使用示例

详细的使用示例请参考 `src/examples/type-usage-example.ts` 文件，其中包含了所有主要接口的使用方法和最佳实践。