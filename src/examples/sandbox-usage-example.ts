import { SandboxManagerImpl } from '../services/SandboxManager';
import { SandboxFileOperations } from '../services/SandboxFileOperations';
import { SandboxConfig, AgentNodeType } from '../types';

/**
 * 沙箱系统使用示例
 * 演示如何创建、使用和清理沙箱环境
 */
async function sandboxUsageExample() {
  console.log('🚀 沙箱系统使用示例开始');

  // 1. 创建沙箱管理器
  const sandboxManager = new SandboxManagerImpl('./example-sandboxes');
  
  try {
    // 2. 为不同的智能体创建沙箱
    const workflowId = 'example-workflow-001';
    const agents: AgentNodeType[] = ['agent_a', 'agent_b', 'agent_c', 'agent_d'];
    
    const sandboxPaths: Record<AgentNodeType, string> = {} as any;
    
    for (const agentId of agents) {
      console.log(`📁 为 ${agentId} 创建沙箱环境...`);
      const sandboxPath = await sandboxManager.createSandbox(workflowId, agentId);
      sandboxPaths[agentId] = sandboxPath;
      
      // 3. 初始化沙箱环境
      const config: SandboxConfig = {
        basePath: sandboxPath,
        nodeId: agentId,
        workflowId,
        permissions: {
          read: true,
          write: true,
          execute: agentId === 'agent_c' // 只有 agent_c 需要执行权限
        },
        resourceLimits: {
          maxMemory: 256 * 1024 * 1024, // 256MB
          maxExecutionTime: 180000, // 3分钟
          maxFileSize: 50 * 1024 * 1024 // 50MB
        }
      };
      
      await sandboxManager.initializeSandbox(sandboxPath, config);
      console.log(`✅ ${agentId} 沙箱环境初始化完成: ${sandboxPath}`);
    }

    // 4. 演示文件操作
    console.log('\n📝 演示安全文件操作...');
    
    // Agent A: 创建输入文件
    const agentAConfig: SandboxConfig = {
      basePath: sandboxPaths.agent_a,
      nodeId: 'agent_a',
      workflowId,
      permissions: { read: true, write: true, execute: false },
      resourceLimits: {
        maxMemory: 256 * 1024 * 1024,
        maxExecutionTime: 180000,
        maxFileSize: 50 * 1024 * 1024
      }
    };
    
    const agentAFileOps = new SandboxFileOperations(agentAConfig);
    
    // 写入初始数据
    await agentAFileOps.writeFile('input/initial_data.json', JSON.stringify({
      task: 'process_data',
      data: [1, 2, 3, 4, 5],
      timestamp: new Date().toISOString()
    }, null, 2));
    
    // 创建处理脚本
    await agentAFileOps.writeFile('scripts/process.js', `
// Agent A 处理脚本
const fs = require('fs');
const path = require('path');

function processData(inputData) {
  return {
    ...inputData,
    processed: true,
    result: inputData.data.map(x => x * 2),
    processedBy: 'agent_a',
    processedAt: new Date().toISOString()
  };
}

module.exports = { processData };
`);
    
    console.log('✅ Agent A 文件创建完成');

    // Agent B: 读取和处理数据
    const agentBConfig: SandboxConfig = {
      basePath: sandboxPaths.agent_b,
      nodeId: 'agent_b',
      workflowId,
      permissions: { read: true, write: true, execute: false },
      resourceLimits: {
        maxMemory: 256 * 1024 * 1024,
        maxExecutionTime: 180000,
        maxFileSize: 50 * 1024 * 1024
      }
    };
    
    const agentBFileOps = new SandboxFileOperations(agentBConfig);
    
    // 模拟从 Agent A 接收数据（在实际系统中会通过工作流传递）
    const simulatedDataFromA = {
      task: 'process_data',
      data: [2, 4, 6, 8, 10], // Agent A 处理后的结果
      processed: true,
      processedBy: 'agent_a'
    };
    
    await agentBFileOps.writeFile('input/data_from_agent_a.json', JSON.stringify(simulatedDataFromA, null, 2));
    
    // Agent B 的多次处理逻辑
    const multipleResults = [];
    for (let i = 0; i < 3; i++) {
      const result = {
        iteration: i + 1,
        input: simulatedDataFromA.data,
        output: simulatedDataFromA.data.map(x => x + i),
        timestamp: new Date().toISOString()
      };
      multipleResults.push(result);
      
      await agentBFileOps.writeFile(`output/iteration_${i + 1}.json`, JSON.stringify(result, null, 2));
    }
    
    // 汇总结果
    await agentBFileOps.writeFile('output/aggregated_results.json', JSON.stringify({
      totalIterations: multipleResults.length,
      results: multipleResults,
      aggregatedBy: 'agent_b',
      aggregatedAt: new Date().toISOString()
    }, null, 2));
    
    console.log('✅ Agent B 多次处理完成');

    // Agent C: 函数执行（模拟）
    const agentCConfig: SandboxConfig = {
      basePath: sandboxPaths.agent_c,
      nodeId: 'agent_c',
      workflowId,
      permissions: { read: true, write: true, execute: true },
      resourceLimits: {
        maxMemory: 256 * 1024 * 1024,
        maxExecutionTime: 180000,
        maxFileSize: 50 * 1024 * 1024
      }
    };
    
    const agentCFileOps = new SandboxFileOperations(agentCConfig);
    
    // 模拟从 Agent A 获取 codeA
    const codeA = 'function transform(data) { return data.map(x => x * 3); }';
    const suffix = 'xxxxdx';
    const functionResult = codeA + suffix;
    
    await agentCFileOps.writeFile('input/code_from_agent_a.js', codeA);
    await agentCFileOps.writeFile('output/function_result.js', functionResult);
    
    // 执行日志
    const executionLog = [
      '开始执行函数组合',
      `原始代码: ${codeA}`,
      `添加后缀: ${suffix}`,
      `最终结果: ${functionResult}`,
      '函数执行完成'
    ];
    
    await agentCFileOps.writeFile('logs/execution.log', executionLog.join('\n'));
    
    console.log('✅ Agent C 函数执行完成');

    // Agent D: 汇聚所有结果
    const agentDConfig: SandboxConfig = {
      basePath: sandboxPaths.agent_d,
      nodeId: 'agent_d',
      workflowId,
      permissions: { read: true, write: true, execute: false },
      resourceLimits: {
        maxMemory: 256 * 1024 * 1024,
        maxExecutionTime: 180000,
        maxFileSize: 50 * 1024 * 1024
      }
    };
    
    const agentDFileOps = new SandboxFileOperations(agentDConfig);
    
    // 模拟汇聚来自 Agent B 和 Agent C 的结果
    const finalResult = {
      workflowId,
      summary: '多智能体协作处理完成',
      agentResults: {
        agent_a: { status: 'completed', output: 'initial_processing' },
        agent_b: { 
          status: 'completed', 
          output: 'multiple_iterations',
          iterations: 3,
          aggregatedData: multipleResults
        },
        agent_c: { 
          status: 'completed', 
          output: 'function_execution',
          functionResult: functionResult
        }
      },
      consolidationReport: {
        sourcesUsed: ['agent_b', 'agent_c'],
        conflictsResolved: [],
        confidenceScore: 0.95
      },
      finalizedAt: new Date().toISOString(),
      finalizedBy: 'agent_d'
    };
    
    await agentDFileOps.writeFile('output/final_result.json', JSON.stringify(finalResult, null, 2));
    
    console.log('✅ Agent D 最终汇聚完成');

    // 5. 展示沙箱统计信息
    console.log('\n📊 沙箱统计信息:');
    for (const agentId of agents) {
      const stats = await sandboxManager.getSandboxStats(sandboxPaths[agentId]);
      console.log(`${agentId}:`);
      console.log(`  - 文件数量: ${stats.fileCount}`);
      console.log(`  - 总大小: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`  - 创建时间: ${stats.createdAt.toLocaleString()}`);
      console.log(`  - 权限: 读取=${stats.permissions.read}, 写入=${stats.permissions.write}, 执行=${stats.permissions.execute}`);
    }

    // 6. 展示文件操作统计
    console.log('\n📈 文件操作统计:');
    const fileOperations = [agentAFileOps, agentBFileOps, agentCFileOps, agentDFileOps];
    const agentNames = ['Agent A', 'Agent B', 'Agent C', 'Agent D'];
    
    fileOperations.forEach((ops, index) => {
      const stats = ops.getOperationStats();
      console.log(`${agentNames[index]}:`);
      console.log(`  - 操作次数: ${stats.operationCount}/${stats.maxOperations}`);
      console.log(`  - 利用率: ${stats.utilizationRate.toFixed(2)}%`);
      console.log(`  - 剩余操作: ${stats.remainingOperations}`);
    });

    // 7. 演示安全验证
    console.log('\n🔒 演示安全验证...');
    
    try {
      // 尝试访问沙箱外的文件（应该失败）
      await agentAFileOps.readFile('../../../etc/passwd');
    } catch (error) {
      console.log('✅ 安全验证生效: 阻止了沙箱外文件访问');
    }
    
    try {
      // 尝试写入危险文件（应该失败）
      await agentAFileOps.writeFile('malware.exe', 'malicious content');
    } catch (error) {
      console.log('✅ 安全验证生效: 阻止了危险文件创建');
    }

    console.log('\n🎉 沙箱系统演示完成！');

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  } finally {
    // 8. 清理所有沙箱
    console.log('\n🧹 清理沙箱环境...');
    await sandboxManager.cleanupAllSandboxes();
    console.log('✅ 所有沙箱环境已清理完成');
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  sandboxUsageExample().catch(console.error);
}

export { sandboxUsageExample };