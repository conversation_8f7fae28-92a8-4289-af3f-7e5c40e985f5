// 类型使用示例
import {
  WorkflowInput,
  WorkflowResult,
  AgentInput,
  AgentOutput,
  AgentAInput,
  AgentBInput,
  AgentCInput,
  AgentDInput,
  LLMAgent,
  FunctionAgent,
  WorkflowManager,
  StorageService,
  MonitoringService,
  AgentNodeType,
  WorkflowStatus,
  ExecutionResult
} from '../types';

// 示例：创建工作流输入
const workflowInput: WorkflowInput = {
  id: 'example-workflow-001',
  parameters: {
    initialPrompt: '分析以下数据并生成报告',
    data: { items: [1, 2, 3, 4, 5] }
  },
  metadata: {
    createdBy: 'user123',
    priority: 'high'
  }
};

// 示例：Agent A 输入配置
const agentAInput: AgentAInput = {
  data: workflowInput.parameters,
  context: { sessionId: 'session-001' },
  sandboxPath: '/tmp/agent_a_sandbox',
  workflowId: 'example-workflow-001',
  nodeId: 'agent_a',
  executionId: 'exec_001',
  timestamp: new Date(),
  initialPrompt: '请分析提供的数据',
  parameters: {
    analysisType: 'statistical',
    outputFormat: 'json'
  }
};

// 示例：Agent B 输入配置（支持多次调用）
const agentBInput: AgentBInput = {
  data: workflowInput.parameters,
  context: { sessionId: 'session-001' },
  sandboxPath: '/tmp/agent_b_sandbox',
  workflowId: 'example-workflow-001',
  nodeId: 'agent_b',
  executionId: 'exec_002',
  timestamp: new Date(),
  prompts: [
    '第一次分析：基础统计',
    '第二次分析：趋势分析',
    '第三次分析：异常检测'
  ],
  multiCallConfig: {
    maxCalls: 3,
    callInterval: 1000,
    aggregationStrategy: 'merge'
  }
};

// 示例：Agent C 输入配置（函数执行）
const agentCInput: AgentCInput = {
  data: { codeA: 'console.log("Hello")' },
  context: { sessionId: 'session-001' },
  sandboxPath: '/tmp/agent_c_sandbox',
  workflowId: 'example-workflow-001',
  nodeId: 'agent_c',
  executionId: 'exec_003',
  timestamp: new Date(),
  codeA: 'console.log("Hello")',
  functionSuffix: 'xxxxdx',
  executionMode: 'safe'
};

// 示例：Agent D 输入配置（汇聚结果）
const agentDInput: AgentDInput = {
  data: {},
  context: { sessionId: 'session-001' },
  sandboxPath: '/tmp/agent_d_sandbox',
  workflowId: 'example-workflow-001',
  nodeId: 'agent_d',
  executionId: 'exec_004',
  timestamp: new Date(),
  agentBResult: {
    result: { analysis: 'completed' },
    metadata: {},
    executionTime: 5000,
    status: 'success',
    nodeId: 'agent_b',
    timestamp: new Date(),
    multipleResults: [],
    aggregatedResult: {},
    callCount: 3,
    callDetails: [
      { callIndex: 1, prompt: '第一次分析', result: {}, executionTime: 1000 },
      { callIndex: 2, prompt: '第二次分析', result: {}, executionTime: 1500 },
      { callIndex: 3, prompt: '第三次分析', result: {}, executionTime: 2500 }
    ]
  },
  agentCResult: {
    result: { function: 'executed' },
    metadata: {},
    executionTime: 1000,
    status: 'success',
    nodeId: 'agent_c',
    timestamp: new Date(),
    functionResult: 'console.log("Hello")xxxxdx',
    executedCode: 'console.log("Hello")xxxxdx',
    executionLog: ['Function executed successfully'],
    securityChecks: {
      passed: true,
      warnings: []
    }
  },
  aggregationPrompt: '请汇总以上所有分析结果',
  finalOutputFormat: 'structured'
};

// 示例：执行结果记录
const executionResult: ExecutionResult = {
  resultId: 'result_001',
  workflowId: 'example-workflow-001',
  nodeId: 'agent_a',
  nodeType: 'llm',
  startTime: new Date('2024-01-01T10:00:00Z'),
  endTime: new Date('2024-01-01T10:05:00Z'),
  executionTime: 300000,
  input: agentAInput,
  output: {
    result: { codeA: 'processed_code', analysisResult: 'analysis_complete' },
    metadata: { model: 'gpt-4', tokens: 1500 },
    executionTime: 300000,
    status: 'success',
    nodeId: 'agent_a',
    timestamp: new Date()
  },
  status: 'success',
  executionPath: '/tmp/agent_a_sandbox',
  retryCount: 0,
  sandboxPath: '/tmp/agent_a_sandbox',
  resourceUsage: {
    memory: 128,
    cpu: 25,
    diskIO: 1024
  },
  metadata: {
    nodeType: 'llm',
    model: 'gpt-4'
  }
};

// 示例：工作流状态
const workflowStatus: WorkflowStatus = {
  workflowId: 'example-workflow-001',
  status: 'running',
  currentNode: 'agent_b',
  progress: 50,
  startTime: new Date('2024-01-01T10:00:00Z'),
  nodes: [
    {
      nodeId: 'agent_a',
      status: 'completed',
      startTime: new Date('2024-01-01T10:00:00Z'),
      endTime: new Date('2024-01-01T10:05:00Z'),
      executionTime: 300000,
      output: { result: 'success' },
      retryCount: 0,
      progress: 100,
      dependencies: [],
      dependenciesCompleted: []
    },
    {
      nodeId: 'agent_b',
      status: 'running',
      startTime: new Date('2024-01-01T10:05:00Z'),
      retryCount: 0,
      progress: 50,
      dependencies: [],
      dependenciesCompleted: []
    }
  ],
  executionPath: ['agent_a'],
  checkpoints: [
    {
      checkpointId: 'checkpoint-001',
      workflowId: 'example-workflow-001',
      timestamp: new Date('2024-01-01T10:05:00Z'),
      completedNodes: ['agent_a'],
      currentState: { agentAResult: 'completed' },
      nextNodes: ['agent_b', 'agent_c']
    }
  ]
};

// 示例：LLM Agent 接口实现类型
class ExampleLLMAgent implements LLMAgent {
  nodeId: AgentNodeType = 'agent_a';
  nodeType: 'llm' = 'llm';
  llmConfig = {
    provider: 'openai',
    model: 'gpt-4',
    apiKey: 'sk-xxx',
    temperature: 0.7,
    maxTokens: 2000
  };

  async initialize(): Promise<void> {
    // 初始化逻辑
  }

  async execute(input: AgentInput): Promise<AgentOutput> {
    // 实现 LLM 调用逻辑
    return {
      result: { codeA: 'generated_code', analysisResult: 'analysis_done' },
      metadata: { model: 'gpt-4' },
      executionTime: 5000,
      status: 'success' as const,
      nodeId: this.nodeId,
      timestamp: new Date()
    };
  }

  async cleanup(): Promise<void> {
    // 清理逻辑
  }

  getStatus() {
    return {
      nodeId: this.nodeId,
      isInitialized: true,
      isExecuting: false,
      lastExecutionTime: new Date(),
      errorCount: 0,
      successCount: 1,
      currentLoad: 0
    };
  }

  async callLLM(prompt: string, context?: Record<string, any>): Promise<string> {
    // 实现 LLM 调用
    return 'LLM response';
  }

  async retry(input: AgentInput, maxRetries: number): Promise<AgentOutput> {
    // 实现重试逻辑
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.execute(input);
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
    throw new Error('Max retries exceeded');
  }

  validateOutput(output: any): boolean {
    return output && typeof output === 'object';
  }

  getModel(): string {
    return 'gpt-4';
  }

  getSandboxPath(): string {
    return '/tmp/agent_a_sandbox';
  }
}

// 示例：Function Agent 接口实现类型
class ExampleFunctionAgent implements FunctionAgent {
  nodeId: AgentNodeType = 'agent_c';
  nodeType: 'function' = 'function';

  async initialize(): Promise<void> {
    // 初始化逻辑
  }

  async execute(input: AgentInput): Promise<AgentOutput> {
    const agentCInput = input as AgentCInput;
    const result = await this.executeFunction(agentCInput.codeA, agentCInput.functionSuffix);
    return {
      result: { functionResult: result, executedCode: agentCInput.codeA + agentCInput.functionSuffix },
      metadata: { executionType: 'function' },
      executionTime: 1000,
      status: 'success' as const,
      nodeId: this.nodeId,
      timestamp: new Date()
    };
  }

  async cleanup(): Promise<void> {
    // 清理逻辑
  }

  getStatus() {
    return {
      nodeId: this.nodeId,
      isInitialized: true,
      isExecuting: false,
      lastExecutionTime: new Date(),
      errorCount: 0,
      successCount: 1,
      currentLoad: 0
    };
  }

  async executeFunction(codeA: string, suffix: string = 'xxxxdx'): Promise<string> {
    // 实现函数执行逻辑
    return codeA + suffix;
  }

  validateFunctionInput(input: any): boolean {
    return input && typeof input.codeA === 'string';
  }

  getSandboxPath(): string {
    return '/tmp/agent_c_sandbox';
  }
}

// 导出示例以供参考
export {
  workflowInput,
  agentAInput,
  agentBInput,
  agentCInput,
  agentDInput,
  executionResult,
  workflowStatus,
  ExampleLLMAgent,
  ExampleFunctionAgent
};