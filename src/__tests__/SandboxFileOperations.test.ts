import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs/promises';
import * as path from 'path';
import { SandboxFileOperations, FileStats, OperationStats } from '../services/SandboxFileOperations';
import { SandboxConfig } from '../types';

// Mock logger
vi.mock('../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }
}));

describe('SandboxFileOperations', () => {
  let fileOps: SandboxFileOperations;
  let testSandboxPath: string;
  let testBasePath: string;
  let config: SandboxConfig;

  beforeEach(async () => {
    testBasePath = path.join(process.cwd(), 'test-file-ops-sandbox');
    testSandboxPath = path.join(testBasePath, 'sandbox');
    
    // 创建测试目录
    await fs.mkdir(testSandboxPath, { recursive: true });
    
    config = {
      basePath: testSandboxPath,
      nodeId: 'agent_a',
      workflowId: 'test-workflow',
      permissions: {
        read: true,
        write: true,
        execute: false
      },
      resourceLimits: {
        maxMemory: 512 * 1024 * 1024,
        maxExecutionTime: 300000,
        maxFileSize: 10 * 1024 * 1024 // 10MB for testing
      }
    };
    
    fileOps = new SandboxFileOperations(config);
  });

  afterEach(async () => {
    // 清理测试目录
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch {
      // 目录不存在，忽略错误
    }
  });

  describe('readFile', () => {
    let testFilePath: string;
    const testContent = 'Hello, World!\nThis is a test file.';

    beforeEach(async () => {
      testFilePath = path.join(testSandboxPath, 'test.txt');
      await fs.writeFile(testFilePath, testContent);
    });

    it('应该成功读取文件', async () => {
      const content = await fileOps.readFile(testFilePath);
      expect(content).toBe(testContent);
    });

    it('应该支持不同的编码格式', async () => {
      const content = await fileOps.readFile(testFilePath, 'utf-8');
      expect(content).toBe(testContent);
    });

    it('应该拒绝读取沙箱外的文件', async () => {
      const outsideFilePath = path.join(testBasePath, '../outside.txt');
      await expect(fileOps.readFile(outsideFilePath))
        .rejects.toThrow('文件操作安全验证失败');
    });

    it('应该在没有读取权限时拒绝操作', async () => {
      const noReadConfig = { ...config, permissions: { ...config.permissions, read: false } };
      const noReadFileOps = new SandboxFileOperations(noReadConfig);
      
      await expect(noReadFileOps.readFile(testFilePath))
        .rejects.toThrow('没有read操作权限');
    });

    it('应该处理不存在的文件', async () => {
      const nonExistentPath = path.join(testSandboxPath, 'nonexistent.txt');
      await expect(fileOps.readFile(nonExistentPath))
        .rejects.toThrow('文件操作安全验证失败');
    });
  });

  describe('writeFile', () => {
    let testFilePath: string;
    const testContent = 'New file content';

    beforeEach(() => {
      testFilePath = path.join(testSandboxPath, 'new.txt');
    });

    it('应该成功写入文件', async () => {
      await fileOps.writeFile(testFilePath, testContent);
      
      const writtenContent = await fs.readFile(testFilePath, 'utf-8');
      expect(writtenContent).toBe(testContent);
    });

    it('应该创建必要的目录', async () => {
      const nestedFilePath = path.join(testSandboxPath, 'nested', 'dir', 'file.txt');
      await fileOps.writeFile(nestedFilePath, testContent);
      
      const writtenContent = await fs.readFile(nestedFilePath, 'utf-8');
      expect(writtenContent).toBe(testContent);
    });

    it('应该拒绝超出大小限制的文件', async () => {
      const largeContent = 'x'.repeat(config.resourceLimits.maxFileSize + 1);
      
      await expect(fileOps.writeFile(testFilePath, largeContent))
        .rejects.toThrow('文件内容超出大小限制');
    });

    it('应该在没有写入权限时拒绝操作', async () => {
      const noWriteConfig = { ...config, permissions: { ...config.permissions, write: false } };
      const noWriteFileOps = new SandboxFileOperations(noWriteConfig);
      
      await expect(noWriteFileOps.writeFile(testFilePath, testContent))
        .rejects.toThrow('没有write操作权限');
    });

    it('应该拒绝写入沙箱外的文件', async () => {
      const outsideFilePath = path.join(testBasePath, '../outside.txt');
      
      await expect(fileOps.writeFile(outsideFilePath, testContent))
        .rejects.toThrow('文件操作安全验证失败');
    });
  });

  describe('appendFile', () => {
    let testFilePath: string;
    const initialContent = 'Initial content\n';
    const appendContent = 'Appended content\n';

    beforeEach(async () => {
      testFilePath = path.join(testSandboxPath, 'append.txt');
      await fs.writeFile(testFilePath, initialContent);
    });

    it('应该成功追加文件内容', async () => {
      await fileOps.appendFile(testFilePath, appendContent);
      
      const finalContent = await fs.readFile(testFilePath, 'utf-8');
      expect(finalContent).toBe(initialContent + appendContent);
    });

    it('应该能够追加到新文件', async () => {
      const newFilePath = path.join(testSandboxPath, 'new-append.txt');
      await fileOps.appendFile(newFilePath, appendContent);
      
      const content = await fs.readFile(newFilePath, 'utf-8');
      expect(content).toBe(appendContent);
    });
  });

  describe('deleteFile', () => {
    let testFilePath: string;

    beforeEach(async () => {
      testFilePath = path.join(testSandboxPath, 'delete-me.txt');
      await fs.writeFile(testFilePath, 'content to delete');
    });

    it('应该成功删除文件', async () => {
      await fileOps.deleteFile(testFilePath);
      
      await expect(fs.stat(testFilePath)).rejects.toThrow();
    });

    it('应该处理不存在的文件', async () => {
      const nonExistentPath = path.join(testSandboxPath, 'nonexistent.txt');
      
      await expect(fileOps.deleteFile(nonExistentPath))
        .rejects.toThrow('文件操作安全验证失败');
    });
  });

  describe('createDirectory', () => {
    it('应该成功创建目录', async () => {
      const dirPath = path.join(testSandboxPath, 'new-directory');
      await fileOps.createDirectory(dirPath);
      
      const stats = await fs.stat(dirPath);
      expect(stats.isDirectory()).toBe(true);
    });

    it('应该创建嵌套目录', async () => {
      const nestedDirPath = path.join(testSandboxPath, 'nested', 'deep', 'directory');
      await fileOps.createDirectory(nestedDirPath);
      
      const stats = await fs.stat(nestedDirPath);
      expect(stats.isDirectory()).toBe(true);
    });

    it('应该处理已存在的目录', async () => {
      const dirPath = path.join(testSandboxPath, 'existing');
      await fs.mkdir(dirPath);
      
      // 不应该抛出错误
      await expect(fileOps.createDirectory(dirPath)).resolves.not.toThrow();
    });
  });

  describe('deleteDirectory', () => {
    let testDirPath: string;

    beforeEach(async () => {
      testDirPath = path.join(testSandboxPath, 'delete-dir');
      await fs.mkdir(testDirPath, { recursive: true });
      await fs.writeFile(path.join(testDirPath, 'file.txt'), 'content');
    });

    it('应该成功删除目录及其内容', async () => {
      await fileOps.deleteDirectory(testDirPath);
      
      await expect(fs.stat(testDirPath)).rejects.toThrow();
    });
  });

  describe('listDirectory', () => {
    beforeEach(async () => {
      // 创建测试文件和目录
      await fs.writeFile(path.join(testSandboxPath, 'file1.txt'), 'content1');
      await fs.writeFile(path.join(testSandboxPath, 'file2.txt'), 'content2');
      await fs.mkdir(path.join(testSandboxPath, 'subdir'));
    });

    it('应该列出目录内容', async () => {
      const items = await fileOps.listDirectory(testSandboxPath);
      
      expect(items).toContain('file1.txt');
      expect(items).toContain('file2.txt');
      expect(items).toContain('subdir');
    });

    it('应该处理空目录', async () => {
      const emptyDirPath = path.join(testSandboxPath, 'empty');
      await fs.mkdir(emptyDirPath);
      
      const items = await fileOps.listDirectory(emptyDirPath);
      expect(items).toHaveLength(0);
    });
  });

  describe('getFileStats', () => {
    let testFilePath: string;
    const testContent = 'Test file content';

    beforeEach(async () => {
      testFilePath = path.join(testSandboxPath, 'stats.txt');
      await fs.writeFile(testFilePath, testContent);
    });

    it('应该返回正确的文件统计信息', async () => {
      const stats = await fileOps.getFileStats(testFilePath);
      
      expect(stats.size).toBe(Buffer.byteLength(testContent));
      expect(stats.isFile).toBe(true);
      expect(stats.isDirectory).toBe(false);
      expect(stats.createdAt).toBeInstanceOf(Date);
      expect(stats.modifiedAt).toBeInstanceOf(Date);
      expect(stats.accessedAt).toBeInstanceOf(Date);
    });

    it('应该正确识别目录', async () => {
      const dirPath = path.join(testSandboxPath, 'test-dir');
      await fs.mkdir(dirPath);
      
      const stats = await fileOps.getFileStats(dirPath);
      expect(stats.isFile).toBe(false);
      expect(stats.isDirectory).toBe(true);
    });
  });

  describe('fileExists', () => {
    let testFilePath: string;

    beforeEach(async () => {
      testFilePath = path.join(testSandboxPath, 'exists.txt');
      await fs.writeFile(testFilePath, 'content');
    });

    it('应该正确检测文件存在', async () => {
      const exists = await fileOps.fileExists(testFilePath);
      expect(exists).toBe(true);
    });

    it('应该正确检测文件不存在', async () => {
      const nonExistentPath = path.join(testSandboxPath, 'nonexistent.txt');
      const exists = await fileOps.fileExists(nonExistentPath);
      expect(exists).toBe(false);
    });

    it('应该处理沙箱外的文件', async () => {
      const outsidePath = path.join(testBasePath, '../outside.txt');
      const exists = await fileOps.fileExists(outsidePath);
      expect(exists).toBe(false);
    });
  });

  describe('copyFile', () => {
    let sourcePath: string;
    let destPath: string;
    const testContent = 'Content to copy';

    beforeEach(async () => {
      sourcePath = path.join(testSandboxPath, 'source.txt');
      destPath = path.join(testSandboxPath, 'destination.txt');
      await fs.writeFile(sourcePath, testContent);
    });

    it('应该成功复制文件', async () => {
      await fileOps.copyFile(sourcePath, destPath);
      
      const copiedContent = await fs.readFile(destPath, 'utf-8');
      expect(copiedContent).toBe(testContent);
      
      // 原文件应该仍然存在
      const originalContent = await fs.readFile(sourcePath, 'utf-8');
      expect(originalContent).toBe(testContent);
    });

    it('应该拒绝复制到沙箱外', async () => {
      const outsideDestPath = path.join(testBasePath, '../outside.txt');
      
      await expect(fileOps.copyFile(sourcePath, outsideDestPath))
        .rejects.toThrow('文件操作安全验证失败');
    });
  });

  describe('moveFile', () => {
    let sourcePath: string;
    let destPath: string;
    const testContent = 'Content to move';

    beforeEach(async () => {
      sourcePath = path.join(testSandboxPath, 'source.txt');
      destPath = path.join(testSandboxPath, 'destination.txt');
      await fs.writeFile(sourcePath, testContent);
    });

    it('应该成功移动文件', async () => {
      await fileOps.moveFile(sourcePath, destPath);
      
      const movedContent = await fs.readFile(destPath, 'utf-8');
      expect(movedContent).toBe(testContent);
      
      // 原文件应该不存在
      await expect(fs.stat(sourcePath)).rejects.toThrow();
    });
  });

  describe('操作统计和限制', () => {
    it('应该正确跟踪操作次数', async () => {
      const initialStats = fileOps.getOperationStats();
      expect(initialStats.operationCount).toBe(0);
      
      // 执行一些操作
      const testFile = path.join(testSandboxPath, 'test.txt');
      await fileOps.writeFile(testFile, 'content');
      await fileOps.readFile(testFile);
      
      const finalStats = fileOps.getOperationStats();
      expect(finalStats.operationCount).toBe(2);
      expect(finalStats.remainingOperations).toBe(finalStats.maxOperations - 2);
    });

    it('应该在达到操作限制时拒绝操作', async () => {
      // 创建一个操作限制很低的配置
      const limitedConfig = { ...config };
      const limitedFileOps = new SandboxFileOperations(limitedConfig);
      
      // 手动设置操作计数到限制
      (limitedFileOps as any).operationCount = 1000;
      
      // 下一次操作应该失败
      const testFile = path.join(testSandboxPath, 'test.txt');
      await expect(limitedFileOps.readFile(testFile))
        .rejects.toThrow('操作次数超出限制');
    });

    it('应该能够重置操作计数', async () => {
      const testFile = path.join(testSandboxPath, 'test.txt');
      await fileOps.writeFile(testFile, 'content');
      
      expect(fileOps.getOperationStats().operationCount).toBe(1);
      
      fileOps.resetOperationCount();
      
      expect(fileOps.getOperationStats().operationCount).toBe(0);
    });

    it('应该正确计算利用率', async () => {
      const testFile = path.join(testSandboxPath, 'test.txt');
      await fileOps.writeFile(testFile, 'content');
      await fileOps.readFile(testFile);
      
      const stats = fileOps.getOperationStats();
      const expectedUtilization = (2 / stats.maxOperations) * 100;
      expect(stats.utilizationRate).toBeCloseTo(expectedUtilization, 2);
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理权限错误', async () => {
      const noPermConfig = {
        ...config,
        permissions: { read: false, write: false, execute: false }
      };
      const noPermFileOps = new SandboxFileOperations(noPermConfig);
      
      const testFile = path.join(testSandboxPath, 'test.txt');
      
      await expect(noPermFileOps.readFile(testFile))
        .rejects.toThrow('没有read操作权限');
      
      await expect(noPermFileOps.writeFile(testFile, 'content'))
        .rejects.toThrow('没有write操作权限');
    });

    it('应该处理路径遍历攻击', async () => {
      const maliciousPath = path.join(testSandboxPath, '../../../etc/passwd');
      
      await expect(fileOps.readFile(maliciousPath))
        .rejects.toThrow('文件操作安全验证失败');
    });

    it('应该处理特殊字符文件名', async () => {
      const specialCharFile = path.join(testSandboxPath, 'file<>name.txt');
      
      // 应该发出警告但不阻止操作（取决于具体的安全策略）
      await expect(fileOps.writeFile(specialCharFile, 'content')).resolves.not.toThrow();
    });

    it('应该处理空文件名', async () => {
      const emptyNamePath = path.join(testSandboxPath, '');
      
      await expect(fileOps.writeFile(emptyNamePath, 'content'))
        .rejects.toThrow();
    });
  });

  describe('性能测试', () => {
    it('应该高效处理大量小文件操作', async () => {
      const startTime = Date.now();
      
      const promises = [];
      for (let i = 0; i < 100; i++) {
        const filePath = path.join(testSandboxPath, `file-${i}.txt`);
        promises.push(fileOps.writeFile(filePath, `content-${i}`));
      }
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 100个小文件操作应该在合理时间内完成
      expect(duration).toBeLessThan(5000); // 5秒
    });
  });
});