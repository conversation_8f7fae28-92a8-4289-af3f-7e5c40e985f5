import { describe, it, expect, beforeEach } from 'vitest';
import { WorkflowManager } from '../services/WorkflowManager';
import { WorkflowInput } from '../types';

describe('WorkflowManager', () => {
  let workflowManager: WorkflowManager;

  beforeEach(() => {
    workflowManager = new WorkflowManager();
  });

  describe('初始化', () => {
    it('应该能够初始化工作流管理器', async () => {
      await expect(workflowManager.initializeWorkflow()).resolves.not.toThrow();
    });
  });

  describe('工作流执行', () => {
    it('应该能够启动工作流', async () => {
      const input: WorkflowInput = {
        parameters: { test: 'value' }
      };

      const result = await workflowManager.executeWorkflow(input);
      
      expect(result).toBeDefined();
      expect(result.workflowId).toBeDefined();
      expect(result.status.status).toBe('running');
    });

    it('应该能够查询工作流状态', async () => {
      const input: WorkflowInput = {
        parameters: { test: 'value' }
      };

      const result = await workflowManager.executeWorkflow(input);
      const status = await workflowManager.getWorkflowStatus(result.workflowId);
      
      expect(status).toBeDefined();
      expect(status?.workflowId).toBe(result.workflowId);
    });

    it('应该能够停止工作流', async () => {
      const input: WorkflowInput = {
        parameters: { test: 'value' }
      };

      const result = await workflowManager.executeWorkflow(input);
      await workflowManager.stopWorkflow(result.workflowId);
      
      const status = await workflowManager.getWorkflowStatus(result.workflowId);
      expect(status?.status).toBe('stopped');
    });
  });

  describe('工作流管理', () => {
    it('应该能够获取所有工作流', async () => {
      const workflows = workflowManager.getAllWorkflows();
      expect(Array.isArray(workflows)).toBe(true);
    });

    it('应该能够清理已完成的工作流', () => {
      expect(() => workflowManager.cleanupCompletedWorkflows()).not.toThrow();
    });
  });
});