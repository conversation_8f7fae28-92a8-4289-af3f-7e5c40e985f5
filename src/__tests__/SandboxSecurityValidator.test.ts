import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs/promises';
import * as path from 'path';
import { SandboxSecurityValidator, ValidationResult, FileOperation } from '../services/SandboxSecurityValidator';

// Mock logger
vi.mock('../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }
}));

describe('SandboxSecurityValidator', () => {
  let validator: SandboxSecurityValidator;
  let testSandboxPath: string;
  let testBasePath: string;

  beforeEach(async () => {
    validator = new SandboxSecurityValidator();
    testBasePath = path.join(process.cwd(), 'test-security-sandbox');
    testSandboxPath = path.join(testBasePath, 'test-sandbox');
    
    // 创建测试目录
    await fs.mkdir(testSandboxPath, { recursive: true });
  });

  afterEach(async () => {
    // 清理测试目录
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch {
      // 目录不存在，忽略错误
    }
  });

  describe('validateFilePath', () => {
    it('应该允许沙箱内的有效路径', () => {
      const validPath = path.join(testSandboxPath, 'test.txt');
      const result = validator.validateFilePath(validPath, testSandboxPath);
      
      expect(result.isValid).toBe(true);
      expect(result.violations).toHaveLength(0);
    });

    it('应该拒绝路径遍历攻击', () => {
      const maliciousPath = path.join(testSandboxPath, '../../../etc/passwd');
      const result = validator.validateFilePath(maliciousPath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
      // 可能会有多种违规，检查是否包含路径相关的错误
      expect(result.violations.some(v => 
        v.includes('文件路径超出沙箱范围') || 
        v.includes('检测到路径遍历攻击尝试') ||
        v.includes('匹配到危险路径模式')
      )).toBe(true);
    });

    it('应该拒绝沙箱外的路径', () => {
      const outsidePath = '/etc/passwd';
      const result = validator.validateFilePath(outsidePath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
      expect(result.violations).toContain('文件路径超出沙箱范围');
    });

    it('应该拒绝危险的文件扩展名', () => {
      const dangerousPath = path.join(testSandboxPath, 'malware.exe');
      const result = validator.validateFilePath(dangerousPath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
      expect(result.violations).toContain('禁止的文件扩展名: .exe');
    });

    it('应该拒绝过长的文件名', () => {
      const longFileName = 'a'.repeat(300) + '.txt';
      const longPath = path.join(testSandboxPath, longFileName);
      const result = validator.validateFilePath(longPath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
      expect(result.violations).toContain('文件名过长');
    });

    it('应该警告特殊字符', () => {
      const specialCharPath = path.join(testSandboxPath, 'test<>file.txt');
      const result = validator.validateFilePath(specialCharPath, testSandboxPath);
      
      expect(result.warnings).toContain('文件名包含特殊字符');
    });

    it('应该拒绝危险路径模式', () => {
      const dangerousPaths = [
        path.join(testSandboxPath, '../etc/passwd'),
        path.join(testSandboxPath, '../usr/bin/bash'),
        path.join(testSandboxPath, '../var/log/system.log')
      ];

      dangerousPaths.forEach(dangerousPath => {
        const result = validator.validateFilePath(dangerousPath, testSandboxPath);
        expect(result.isValid).toBe(false);
      });
    });
  });

  describe('validateFileOperation', () => {
    let testFilePath: string;

    beforeEach(async () => {
      testFilePath = path.join(testSandboxPath, 'test.txt');
      await fs.writeFile(testFilePath, 'test content');
    });

    it('应该允许有效的读取操作', async () => {
      const result = await validator.validateFileOperation('read', testFilePath, testSandboxPath);
      
      expect(result.isValid).toBe(true);
      expect(result.violations).toHaveLength(0);
    });

    it('应该拒绝读取不存在的文件', async () => {
      const nonExistentPath = path.join(testSandboxPath, 'nonexistent.txt');
      const result = await validator.validateFileOperation('read', nonExistentPath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
      expect(result.violations).toContain('文件不存在或无读取权限');
    });

    it('应该允许有效的写入操作', async () => {
      const newFilePath = path.join(testSandboxPath, 'new.txt');
      const result = await validator.validateFileOperation('write', newFilePath, testSandboxPath);
      
      expect(result.isValid).toBe(true);
    });

    it('应该拒绝超出大小限制的文件写入', async () => {
      const largeFilePath = path.join(testSandboxPath, 'large.txt');
      // 创建一个大文件
      const largeContent = 'x'.repeat(200 * 1024 * 1024); // 200MB
      await fs.writeFile(largeFilePath, largeContent);
      
      const maxFileSize = 100 * 1024 * 1024; // 100MB限制
      const result = await validator.validateFileOperation('write', largeFilePath, testSandboxPath, maxFileSize);
      
      expect(result.isValid).toBe(false);
      expect(result.violations.some(v => v.includes('文件大小超出限制'))).toBe(true);
    });

    it('应该拒绝执行不允许的文件类型', async () => {
      const executablePath = path.join(testSandboxPath, 'malware.exe');
      await fs.writeFile(executablePath, 'fake executable');
      
      const result = await validator.validateFileOperation('execute', executablePath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
      // 检查是否包含文件类型相关的错误
      expect(result.violations.some(v => 
        v.includes('不允许执行的文件类型') || 
        v.includes('禁止的文件扩展名')
      )).toBe(true);
    });

    it('应该允许执行允许的脚本文件', async () => {
      const scriptPath = path.join(testSandboxPath, 'script.js');
      await fs.writeFile(scriptPath, 'console.log("hello");');
      await fs.chmod(scriptPath, 0o755);
      
      const result = await validator.validateFileOperation('execute', scriptPath, testSandboxPath);
      
      expect(result.isValid).toBe(true);
    });

    it('应该警告删除受保护的文件', async () => {
      const protectedPath = path.join(testSandboxPath, 'README.md');
      await fs.writeFile(protectedPath, 'protected content');
      
      const result = await validator.validateFileOperation('delete', protectedPath, testSandboxPath);
      
      expect(result.warnings).toContain('尝试删除受保护的文件: README.md');
    });
  });

  describe('validateFileContent', () => {
    let testFilePath: string;

    beforeEach(() => {
      testFilePath = path.join(testSandboxPath, 'content-test.txt');
    });

    it('应该允许安全的文本内容', async () => {
      const safeContent = 'This is safe content\nwith multiple lines\nand normal text.';
      await fs.writeFile(testFilePath, safeContent);
      
      const result = await validator.validateFileContent(testFilePath);
      
      expect(result.isValid).toBe(true);
      expect(result.violations).toHaveLength(0);
    });

    it('应该拒绝超出大小限制的文件', async () => {
      const largeContent = 'x'.repeat(200 * 1024 * 1024); // 200MB
      await fs.writeFile(testFilePath, largeContent);
      
      const maxSize = 100 * 1024 * 1024; // 100MB限制
      const result = await validator.validateFileContent(testFilePath, maxSize);
      
      expect(result.isValid).toBe(false);
      expect(result.violations.some(v => v.includes('文件大小超出限制'))).toBe(true);
    });

    it('应该检测恶意脚本模式', async () => {
      const maliciousContent = `
        const userInput = process.argv[2];
        eval(userInput); // 危险的eval调用
        exec('rm -rf /'); // 危险的系统调用
      `;
      await fs.writeFile(testFilePath, maliciousContent);
      
      const result = await validator.validateFileContent(testFilePath);
      
      expect(result.isValid).toBe(false);
      expect(result.violations).toContain('检测到潜在恶意内容');
    });

    it('应该检测敏感信息', async () => {
      const sensitiveContent = `
        const config = {
          password: "secret123",
          api_key: "sk-1234567890abcdef",
          email: "<EMAIL>"
        };
      `;
      await fs.writeFile(testFilePath, sensitiveContent);
      
      const result = await validator.validateFileContent(testFilePath);
      
      expect(result.warnings).toContain('检测到可能的敏感信息');
    });

    it('应该正确处理二进制文件', async () => {
      const binaryPath = path.join(testSandboxPath, 'binary.jpg');
      const binaryContent = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]); // JPEG头
      await fs.writeFile(binaryPath, binaryContent);
      
      const result = await validator.validateFileContent(binaryPath);
      
      expect(result.warnings).toContain('检测到二进制文件');
    });

    it('应该检测多种恶意模式', async () => {
      const maliciousPatterns = [
        'eval(userInput)',
        'exec("dangerous command")',
        'system("rm -rf /")',
        'shell_exec($cmd)',
        'file_get_contents("http://malicious.com")',
        'chmod 777 /etc/passwd'
      ];

      for (const pattern of maliciousPatterns) {
        const maliciousPath = path.join(testSandboxPath, `malicious-${Date.now()}.txt`);
        await fs.writeFile(maliciousPath, pattern);
        
        const result = await validator.validateFileContent(maliciousPath);
        expect(result.isValid).toBe(false);
        expect(result.violations).toContain('检测到潜在恶意内容');
      }
    });

    it('应该检测多种敏感信息模式', async () => {
      const sensitivePatterns = [
        'password: "secret123"',
        'api_key = "sk-1234567890"',
        'secret="top-secret"',
        'token: "bearer-token-123"',
        '<EMAIL>',
        '4532-1234-5678-9012' // 信用卡号格式
      ];

      for (const pattern of sensitivePatterns) {
        const sensitivePath = path.join(testSandboxPath, `sensitive-${Date.now()}.txt`);
        await fs.writeFile(sensitivePath, pattern);
        
        const result = await validator.validateFileContent(sensitivePath);
        expect(result.warnings).toContain('检测到可能的敏感信息');
      }
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该处理空文件路径', () => {
      const result = validator.validateFilePath('', testSandboxPath);
      expect(result.isValid).toBe(false);
    });

    it('应该处理null或undefined路径', () => {
      expect(() => validator.validateFilePath(null as any, testSandboxPath)).not.toThrow();
      expect(() => validator.validateFilePath(undefined as any, testSandboxPath)).not.toThrow();
    });

    it('应该处理不存在的沙箱路径', () => {
      const nonExistentSandbox = '/non/existent/sandbox';
      const filePath = path.join(nonExistentSandbox, 'test.txt');
      const result = validator.validateFilePath(filePath, nonExistentSandbox);
      
      // 应该仍然进行基本的路径验证
      expect(result).toBeDefined();
    });

    it('应该处理文件系统错误', async () => {
      const inaccessiblePath = '/root/inaccessible.txt';
      const result = await validator.validateFileOperation('read', inaccessiblePath, testSandboxPath);
      
      expect(result.isValid).toBe(false);
    });

    it('应该处理编码错误', async () => {
      const binaryPath = path.join(testSandboxPath, 'binary.bin');
      const binaryContent = Buffer.from([0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE]);
      await fs.writeFile(binaryPath, binaryContent);
      
      // 尝试以文本方式读取二进制文件
      await expect(validator.validateFileContent(binaryPath)).resolves.toBeDefined();
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成大量路径验证', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        const testPath = path.join(testSandboxPath, `test-${i}.txt`);
        validator.validateFilePath(testPath, testSandboxPath);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 1000次验证应该在1秒内完成
      expect(duration).toBeLessThan(1000);
    });

    it('应该高效处理大文件内容验证', async () => {
      const largePath = path.join(testSandboxPath, 'large.txt');
      const largeContent = 'safe content\n'.repeat(100000); // 约1MB
      await fs.writeFile(largePath, largeContent);
      
      const startTime = Date.now();
      await validator.validateFileContent(largePath);
      const endTime = Date.now();
      
      const duration = endTime - startTime;
      // 1MB文件验证应该在500ms内完成
      expect(duration).toBeLessThan(500);
    });
  });
});