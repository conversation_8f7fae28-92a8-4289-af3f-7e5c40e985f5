import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs/promises';
import * as path from 'path';
import { SandboxManagerImpl } from '../services/SandboxManager';
import { SandboxConfig, AgentNodeType } from '../types';

// Mock logger
vi.mock('../utils/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  }
}));

describe('SandboxManager', () => {
  let sandboxManager: SandboxManagerImpl;
  let testBasePath: string;
  let testWorkflowId: string;
  let testNodeId: AgentNodeType;

  beforeEach(async () => {
    testBasePath = path.join(process.cwd(), 'test-sandboxes');
    testWorkflowId = 'test-workflow-123';
    testNodeId = 'agent_a';
    
    sandboxManager = new SandboxManagerImpl(testBasePath);
    
    // 清理测试目录
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch {
      // 目录不存在，忽略错误
    }
  });

  afterEach(async () => {
    // 清理测试目录
    try {
      await fs.rm(testBasePath, { recursive: true, force: true });
    } catch {
      // 目录不存在，忽略错误
    }
  });

  describe('createSandbox', () => {
    it('应该成功创建沙箱环境', async () => {
      const sandboxPath = await sandboxManager.createSandbox(testWorkflowId, testNodeId);
      
      expect(sandboxPath).toContain(testBasePath);
      expect(sandboxPath).toContain(testWorkflowId);
      expect(sandboxPath).toContain(testNodeId);
      
      // 验证目录是否存在
      const stats = await fs.stat(sandboxPath);
      expect(stats.isDirectory()).toBe(true);
    });

    it('应该为不同的节点创建不同的沙箱路径', async () => {
      const sandboxPath1 = await sandboxManager.createSandbox(testWorkflowId, 'agent_a');
      const sandboxPath2 = await sandboxManager.createSandbox(testWorkflowId, 'agent_b');
      
      expect(sandboxPath1).not.toBe(sandboxPath2);
      expect(sandboxPath1).toContain('agent_a');
      expect(sandboxPath2).toContain('agent_b');
    });

    it('应该在活跃沙箱列表中注册新创建的沙箱', async () => {
      const sandboxPath = await sandboxManager.createSandbox(testWorkflowId, testNodeId);
      const activeSandboxes = sandboxManager.getActiveSandboxes();
      
      expect(activeSandboxes.has(sandboxPath)).toBe(true);
    });
  });

  describe('initializeSandbox', () => {
    let sandboxPath: string;
    let config: SandboxConfig;

    beforeEach(async () => {
      sandboxPath = await sandboxManager.createSandbox(testWorkflowId, testNodeId);
      config = {
        basePath: sandboxPath,
        nodeId: testNodeId,
        workflowId: testWorkflowId,
        permissions: {
          read: true,
          write: true,
          execute: false
        },
        resourceLimits: {
          maxMemory: 512 * 1024 * 1024,
          maxExecutionTime: 300000,
          maxFileSize: 100 * 1024 * 1024
        }
      };
    });

    it('应该成功初始化沙箱环境', async () => {
      await expect(sandboxManager.initializeSandbox(sandboxPath, config)).resolves.not.toThrow();
    });

    it('应该创建基本的目录结构', async () => {
      await sandboxManager.initializeSandbox(sandboxPath, config);
      
      const directories = ['input', 'output', 'temp', 'logs'];
      for (const dir of directories) {
        const dirPath = path.join(sandboxPath, dir);
        const stats = await fs.stat(dirPath);
        expect(stats.isDirectory()).toBe(true);
      }
    });

    it('应该创建README文件', async () => {
      await sandboxManager.initializeSandbox(sandboxPath, config);
      
      const readmePath = path.join(sandboxPath, 'README.md');
      const stats = await fs.stat(readmePath);
      expect(stats.isFile()).toBe(true);
      
      const content = await fs.readFile(readmePath, 'utf-8');
      expect(content).toContain('沙箱环境');
    });

    it('应该创建安全策略文件', async () => {
      await sandboxManager.initializeSandbox(sandboxPath, config);
      
      const policyPath = path.join(sandboxPath, '.security_policy.json');
      const stats = await fs.stat(policyPath);
      expect(stats.isFile()).toBe(true);
      
      const content = await fs.readFile(policyPath, 'utf-8');
      const policy = JSON.parse(content);
      expect(policy).toHaveProperty('allowedPaths');
      expect(policy).toHaveProperty('blockedPaths');
      expect(policy).toHaveProperty('resourceLimits');
    });

    it('应该拒绝无效的沙箱路径', async () => {
      const invalidPath = '/invalid/path';
      await expect(sandboxManager.initializeSandbox(invalidPath, config))
        .rejects.toThrow('无效的沙箱路径');
    });
  });

  describe('validateSandboxAccess', () => {
    let sandboxPath: string;
    let config: SandboxConfig;

    beforeEach(async () => {
      sandboxPath = await sandboxManager.createSandbox(testWorkflowId, testNodeId);
      config = {
        basePath: sandboxPath,
        nodeId: testNodeId,
        workflowId: testWorkflowId,
        permissions: {
          read: true,
          write: true,
          execute: false
        },
        resourceLimits: {
          maxMemory: 512 * 1024 * 1024,
          maxExecutionTime: 300000,
          maxFileSize: 100 * 1024 * 1024
        }
      };
      await sandboxManager.initializeSandbox(sandboxPath, config);
    });

    it('应该允许有权限的操作', () => {
      expect(sandboxManager.validateSandboxAccess(sandboxPath, 'read')).toBe(true);
      expect(sandboxManager.validateSandboxAccess(sandboxPath, 'write')).toBe(true);
    });

    it('应该拒绝无权限的操作', () => {
      expect(sandboxManager.validateSandboxAccess(sandboxPath, 'execute')).toBe(false);
    });

    it('应该拒绝未注册沙箱的访问', () => {
      const unregisteredPath = '/unregistered/path';
      expect(sandboxManager.validateSandboxAccess(unregisteredPath, 'read')).toBe(false);
    });
  });

  describe('getSandboxStats', () => {
    let sandboxPath: string;

    beforeEach(async () => {
      sandboxPath = await sandboxManager.createSandbox(testWorkflowId, testNodeId);
      const config: SandboxConfig = {
        basePath: sandboxPath,
        nodeId: testNodeId,
        workflowId: testWorkflowId,
        permissions: {
          read: true,
          write: true,
          execute: false
        },
        resourceLimits: {
          maxMemory: 512 * 1024 * 1024,
          maxExecutionTime: 300000,
          maxFileSize: 100 * 1024 * 1024
        }
      };
      await sandboxManager.initializeSandbox(sandboxPath, config);
    });

    it('应该返回正确的沙箱统计信息', async () => {
      const stats = await sandboxManager.getSandboxStats(sandboxPath);
      
      expect(stats.path).toBe(sandboxPath);
      expect(stats.fileCount).toBeGreaterThan(0); // 至少有README文件
      expect(stats.size).toBeGreaterThan(0);
      expect(stats.createdAt).toBeInstanceOf(Date);
      expect(stats.permissions).toEqual({
        read: true,
        write: true,
        execute: false
      });
    });

    it('应该正确计算文件数量', async () => {
      // 创建一些测试文件
      const testFile1 = path.join(sandboxPath, 'input', 'test1.txt');
      const testFile2 = path.join(sandboxPath, 'output', 'test2.txt');
      
      await fs.writeFile(testFile1, 'test content 1');
      await fs.writeFile(testFile2, 'test content 2');
      
      const stats = await sandboxManager.getSandboxStats(sandboxPath);
      expect(stats.fileCount).toBeGreaterThanOrEqual(3); // README + 2个测试文件 + 安全策略文件
    });
  });

  describe('cleanupSandbox', () => {
    let sandboxPath: string;

    beforeEach(async () => {
      sandboxPath = await sandboxManager.createSandbox(testWorkflowId, testNodeId);
      const config: SandboxConfig = {
        basePath: sandboxPath,
        nodeId: testNodeId,
        workflowId: testWorkflowId,
        permissions: {
          read: true,
          write: true,
          execute: false
        },
        resourceLimits: {
          maxMemory: 512 * 1024 * 1024,
          maxExecutionTime: 300000,
          maxFileSize: 100 * 1024 * 1024
        }
      };
      await sandboxManager.initializeSandbox(sandboxPath, config);
    });

    it('应该成功清理沙箱环境', async () => {
      // 验证沙箱存在
      const statsBefore = await fs.stat(sandboxPath);
      expect(statsBefore.isDirectory()).toBe(true);
      
      // 清理沙箱
      await sandboxManager.cleanupSandbox(sandboxPath);
      
      // 验证沙箱已被删除
      await expect(fs.stat(sandboxPath)).rejects.toThrow();
    });

    it('应该从活跃沙箱列表中移除已清理的沙箱', async () => {
      const activeSandboxesBefore = sandboxManager.getActiveSandboxes();
      expect(activeSandboxesBefore.has(sandboxPath)).toBe(true);
      
      await sandboxManager.cleanupSandbox(sandboxPath);
      
      const activeSandboxesAfter = sandboxManager.getActiveSandboxes();
      expect(activeSandboxesAfter.has(sandboxPath)).toBe(false);
    });

    it('应该优雅处理不存在的沙箱路径', async () => {
      const nonExistentPath = path.join(testBasePath, 'non-existent');
      await expect(sandboxManager.cleanupSandbox(nonExistentPath)).resolves.not.toThrow();
    });
  });

  describe('cleanupAllSandboxes', () => {
    it('应该清理所有活跃的沙箱', async () => {
      // 创建多个沙箱
      const sandboxPath1 = await sandboxManager.createSandbox(testWorkflowId, 'agent_a');
      const sandboxPath2 = await sandboxManager.createSandbox(testWorkflowId, 'agent_b');
      const sandboxPath3 = await sandboxManager.createSandbox(testWorkflowId, 'agent_c');
      
      // 验证沙箱存在
      expect(sandboxManager.getActiveSandboxes().size).toBe(3);
      
      // 清理所有沙箱
      await sandboxManager.cleanupAllSandboxes();
      
      // 验证所有沙箱都已清理
      expect(sandboxManager.getActiveSandboxes().size).toBe(0);
      
      // 验证目录已被删除
      await expect(fs.stat(sandboxPath1)).rejects.toThrow();
      await expect(fs.stat(sandboxPath2)).rejects.toThrow();
      await expect(fs.stat(sandboxPath3)).rejects.toThrow();
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该处理权限不足的情况', async () => {
      // 这个测试在某些环境中可能无法运行，因为需要特定的文件系统权限
      // 可以根据实际环境调整或跳过
    });

    it('应该处理磁盘空间不足的情况', async () => {
      // 模拟磁盘空间不足的情况比较困难，可以通过mock fs模块来实现
    });

    it('应该处理并发访问的情况', async () => {
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(sandboxManager.createSandbox(`workflow-${i}`, 'agent_a'));
      }
      
      const results = await Promise.all(promises);
      expect(results.length).toBe(10);
      expect(new Set(results).size).toBe(10); // 所有路径都应该是唯一的
    });
  });
});