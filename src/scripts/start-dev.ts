#!/usr/bin/env ts-node

import { config, validateConfig } from '../config';
import { logger } from '../utils/logger';

async function startDevelopment() {
  try {
    logger.info('开始启动开发环境');
    
    // 验证配置
    validateConfig();
    logger.info('配置验证通过');
    
    // 启动服务器
    logger.info(`准备在端口 ${config.port} 启动服务器`);
    
    // 导入并启动应用
    await import('../index');
    
  } catch (error) {
    logger.error('启动失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  startDevelopment();
}