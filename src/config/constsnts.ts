/**
 * 本地mongoDB
 */
export const CHECKPOINTER_CONFIG = {
  uri: '*************************************************************************************',
  dbName: 'langgraph_multi_agent',
  checkpointCollectionName: 'checkpoints',
  checkpointWritesCollectionName: 'checkpoint_writes'
};

/**
 * langfuse配置
 */
export const LANGFUSE_CONFIG = {
  publicKey: 'pk-lf-658195da-ea24-4d87-a398-9f70f602a0c0',
  secretKey: 'sk-lf-8a7637d9-8a82-41cf-80a0-4aadf60204bb',
  baseUrl: 'http://************:8090/fst-observe-pipeline/connect/langfuse',
};

export const DEFAULT_OPENAI_API_KEY='sk-cITab2RRLmsfbUH-pNJg';
export const DEFAULT_OPENAI_BASE_URL='http://*************/web/unauth/LLM_api_proxy/v1';
export const DEFAULT_MODEL='ht::saas-deepseek-v3';
