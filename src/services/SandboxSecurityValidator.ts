import * as path from 'path';
import * as fs from 'fs/promises';
import { logger } from '../utils/logger';

/**
 * 沙箱安全验证器
 * 负责验证文件操作的安全性和合规性
 */
export class SandboxSecurityValidator {
  private readonly blockedExtensions = [
    '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
    '.msi', '.dll', '.so', '.dylib'
  ];

  private readonly blockedPatterns = [
    /\.\./g, // 路径遍历
    /\/etc\//g, // 系统配置目录
    /\/usr\//g, // 系统程序目录
    /\/bin\//g, // 系统二进制目录
    /\/sbin\//g, // 系统管理二进制目录
    /\/var\//g, // 系统变量目录
    /\/tmp\//g, // 临时目录
    /\/proc\//g, // 进程信息目录
    /\/sys\//g // 系统信息目录
  ];

  /**
   * 验证文件路径是否安全
   */
  validateFilePath(filePath: string, sandboxPath: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      violations: [],
      warnings: []
    };

    try {
      // 规范化路径
      const normalizedPath = path.normalize(filePath);
      const normalizedSandboxPath = path.normalize(sandboxPath);

      // 检查路径遍历攻击
      if (this.hasPathTraversal(normalizedPath)) {
        result.isValid = false;
        result.violations.push('检测到路径遍历攻击尝试');
      }

      // 检查是否在沙箱范围内
      if (!this.isWithinSandbox(normalizedPath, normalizedSandboxPath)) {
        result.isValid = false;
        result.violations.push('文件路径超出沙箱范围');
      }

      // 检查文件扩展名
      const extension = path.extname(normalizedPath).toLowerCase();
      if (this.blockedExtensions.includes(extension)) {
        result.isValid = false;
        result.violations.push(`禁止的文件扩展名: ${extension}`);
      }

      // 检查危险路径模式
      for (const pattern of this.blockedPatterns) {
        if (pattern.test(normalizedPath)) {
          result.isValid = false;
          result.violations.push(`匹配到危险路径模式: ${pattern.source}`);
        }
      }

      // 检查文件名长度
      const fileName = path.basename(normalizedPath);
      if (fileName.length > 255) {
        result.isValid = false;
        result.violations.push('文件名过长');
      }

      // 检查特殊字符
      if (this.hasSpecialCharacters(fileName)) {
        result.warnings.push('文件名包含特殊字符');
      }

    } catch (error) {
      result.isValid = false;
      result.violations.push(`路径验证失败: ${error}`);
    }

    return result;
  }

  /**
   * 验证文件操作是否安全
   */
  async validateFileOperation(
    operation: FileOperation,
    filePath: string,
    sandboxPath: string,
    maxFileSize?: number
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      violations: [],
      warnings: []
    };

    try {
      // 首先验证路径
      const pathValidation = this.validateFilePath(filePath, sandboxPath);
      if (!pathValidation.isValid) {
        return pathValidation;
      }

      // 根据操作类型进行特定验证
      switch (operation) {
        case 'read':
          await this.validateReadOperation(filePath, result);
          break;
        case 'write':
          await this.validateWriteOperation(filePath, result, maxFileSize);
          break;
        case 'execute':
          await this.validateExecuteOperation(filePath, result);
          break;
        case 'delete':
          await this.validateDeleteOperation(filePath, result);
          break;
      }

    } catch (error) {
      result.isValid = false;
      result.violations.push(`文件操作验证失败: ${error}`);
    }

    return result;
  }

  /**
   * 验证文件内容是否安全
   */
  async validateFileContent(filePath: string, maxSize: number = 100 * 1024 * 1024): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      violations: [],
      warnings: []
    };

    try {
      const stats = await fs.stat(filePath);

      // 检查文件大小
      if (stats.size > maxSize) {
        result.isValid = false;
        result.violations.push(`文件大小超出限制: ${stats.size} > ${maxSize}`);
        return result;
      }

      // 检查文件类型
      const extension = path.extname(filePath).toLowerCase();
      if (this.isBinaryFile(extension)) {
        result.warnings.push('检测到二进制文件');
        return result; // 二进制文件不进行内容检查
      }

      // 读取文件内容进行检查
      const content = await fs.readFile(filePath, 'utf-8');

      // 检查恶意脚本模式
      if (this.hasMaliciousPatterns(content)) {
        result.isValid = false;
        result.violations.push('检测到潜在恶意内容');
      }

      // 检查敏感信息
      if (this.hasSensitiveInformation(content)) {
        result.warnings.push('检测到可能的敏感信息');
      }

    } catch (error) {
      result.violations.push(`文件内容验证失败: ${error}`);
    }

    return result;
  }

  /**
   * 检查路径遍历攻击
   */
  private hasPathTraversal(filePath: string): boolean {
    // 检查相对路径中的父目录引用
    if (filePath.includes('..')) {
      return true;
    }
    
    // 检查用户主目录引用
    if (filePath.includes('~')) {
      return true;
    }
    
    // 在Windows和Unix系统中，绝对路径可能是合法的，只要它在沙箱内
    // 这个检查将在isWithinSandbox中进行
    return false;
  }

  /**
   * 检查路径是否在沙箱范围内
   */
  private isWithinSandbox(filePath: string, sandboxPath: string): boolean {
    try {
      const resolvedFilePath = path.resolve(filePath);
      const resolvedSandboxPath = path.resolve(sandboxPath);
      
      // 确保路径以分隔符结尾，避免部分匹配问题
      const normalizedSandboxPath = resolvedSandboxPath.endsWith(path.sep) 
        ? resolvedSandboxPath 
        : resolvedSandboxPath + path.sep;
      
      return resolvedFilePath.startsWith(normalizedSandboxPath) || 
             resolvedFilePath === resolvedSandboxPath;
    } catch {
      return false;
    }
  }

  /**
   * 检查文件名是否包含特殊字符
   */
  private hasSpecialCharacters(fileName: string): boolean {
    const specialChars = /[<>:"|?*\x00-\x1f]/;
    return specialChars.test(fileName);
  }

  /**
   * 验证读取操作
   */
  private async validateReadOperation(filePath: string, result: ValidationResult): Promise<void> {
    try {
      await fs.access(filePath, fs.constants.R_OK);
    } catch {
      result.isValid = false;
      result.violations.push('文件不存在或无读取权限');
    }
  }

  /**
   * 验证写入操作
   */
  private async validateWriteOperation(
    filePath: string, 
    result: ValidationResult, 
    maxFileSize?: number
  ): Promise<void> {
    try {
      const dirPath = path.dirname(filePath);
      
      // 递归检查目录层次，找到第一个存在的父目录
      let currentPath = dirPath;
      let foundWritableParent = false;
      
      while (currentPath !== path.dirname(currentPath)) { // 避免无限循环
        try {
          await fs.access(currentPath, fs.constants.W_OK);
          foundWritableParent = true;
          break;
        } catch {
          currentPath = path.dirname(currentPath);
        }
      }
      
      if (!foundWritableParent) {
        result.isValid = false;
        result.violations.push('目录不存在或无写入权限');
        return;
      }

      // 如果文件已存在，检查大小限制
      if (maxFileSize) {
        try {
          const stats = await fs.stat(filePath);
          if (stats.size > maxFileSize) {
            result.isValid = false;
            result.violations.push(`文件大小超出限制: ${stats.size} > ${maxFileSize}`);
          }
        } catch {
          // 文件不存在，这是正常的
        }
      }
    } catch (error) {
      result.isValid = false;
      result.violations.push('目录不存在或无写入权限');
    }
  }

  /**
   * 验证执行操作
   */
  private async validateExecuteOperation(filePath: string, result: ValidationResult): Promise<void> {
    try {
      await fs.access(filePath, fs.constants.X_OK);
      
      // 额外的执行安全检查
      const extension = path.extname(filePath).toLowerCase();
      const allowedExecutableExtensions = ['.js', '.ts', '.py', '.sh'];
      
      if (!allowedExecutableExtensions.includes(extension)) {
        result.isValid = false;
        result.violations.push(`不允许执行的文件类型: ${extension}`);
      }
    } catch {
      result.isValid = false;
      result.violations.push('文件不存在或无执行权限');
    }
  }

  /**
   * 验证删除操作
   */
  private async validateDeleteOperation(filePath: string, result: ValidationResult): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      
      // 检查是否为重要系统文件
      const fileName = path.basename(filePath);
      const protectedFiles = ['README.md', '.security_policy.json'];
      
      if (protectedFiles.includes(fileName)) {
        result.warnings.push(`尝试删除受保护的文件: ${fileName}`);
      }
    } catch {
      result.isValid = false;
      result.violations.push('文件不存在');
    }
  }

  /**
   * 检查是否为二进制文件
   */
  private isBinaryFile(extension: string): boolean {
    const binaryExtensions = [
      '.exe', '.dll', '.so', '.dylib', '.bin', '.dat',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico',
      '.mp3', '.mp4', '.avi', '.mov', '.wav',
      '.zip', '.rar', '.tar', '.gz', '.7z'
    ];
    return binaryExtensions.includes(extension);
  }

  /**
   * 检查恶意脚本模式
   */
  private hasMaliciousPatterns(content: string): boolean {
    const maliciousPatterns = [
      /eval\s*\(/gi,
      /exec\s*\(/gi,
      /system\s*\(/gi,
      /shell_exec\s*\(/gi,
      /passthru\s*\(/gi,
      /proc_open\s*\(/gi,
      /popen\s*\(/gi,
      /file_get_contents\s*\(\s*["']https?:\/\//gi,
      /curl\s+.*\s+\|/gi,
      /wget\s+.*\s+\|/gi,
      /rm\s+-rf\s+\//gi,
      /chmod\s+777/gi
    ];

    return maliciousPatterns.some(pattern => pattern.test(content));
  }

  /**
   * 检查敏感信息
   */
  private hasSensitiveInformation(content: string): boolean {
    const sensitivePatterns = [
      /password\s*[:=]\s*["'][^"']+["']/gi,
      /api[_-]?key\s*[:=]\s*["'][^"']+["']/gi,
      /secret\s*[:=]\s*["'][^"']+["']/gi,
      /token\s*[:=]\s*["'][^"']+["']/gi,
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // 邮箱
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g // 信用卡号格式
    ];

    return sensitivePatterns.some(pattern => pattern.test(content));
  }
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  violations: string[];
  warnings: string[];
}

/**
 * 文件操作类型
 */
export type FileOperation = 'read' | 'write' | 'execute' | 'delete';