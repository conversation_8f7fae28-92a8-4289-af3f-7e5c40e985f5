import * as fs from 'fs/promises';
import * as path from 'path';
import { 
  SandboxManager, 
  SandboxConfig, 
  SandboxStats, 
  AgentNodeType 
} from '../types';
import { logger } from '../utils/logger';

/**
 * 沙箱环境管理器实现
 * 负责创建、管理和清理智能体的沙箱环境
 */
export class SandboxManagerImpl implements SandboxManager {
  private readonly basePath: string;
  private readonly activeSandboxes: Map<string, SandboxConfig> = new Map();
  private readonly securityPolicies: Map<string, SecurityPolicy> = new Map();

  constructor(basePath: string = './sandboxes') {
    this.basePath = path.resolve(basePath);
  }

  /**
   * 为指定的工作流和节点创建沙箱环境
   */
  async createSandbox(workflowId: string, nodeId: AgentNodeType): Promise<string> {
    try {
      const sandboxId = `${workflowId}_${nodeId}_${Date.now()}`;
      const sandboxPath = path.join(this.basePath, workflowId, nodeId, sandboxId);

      // 确保基础目录存在
      await this.ensureDirectoryExists(this.basePath);
      await this.ensureDirectoryExists(path.dirname(sandboxPath));
      
      // 创建沙箱目录
      await fs.mkdir(sandboxPath, { recursive: true });

      // 设置默认配置
      const config: SandboxConfig = {
        basePath: sandboxPath,
        nodeId,
        workflowId,
        permissions: {
          read: true,
          write: true,
          execute: false // 默认不允许执行，需要显式启用
        },
        resourceLimits: {
          maxMemory: 512 * 1024 * 1024, // 512MB
          maxExecutionTime: 300000, // 5分钟
          maxFileSize: 100 * 1024 * 1024 // 100MB
        }
      };

      // 注册沙箱
      this.activeSandboxes.set(sandboxPath, config);
      
      logger.info(`沙箱环境已创建: ${sandboxPath}`, {
        workflowId,
        nodeId,
        sandboxId
      });

      return sandboxPath;
    } catch (error) {
      logger.error(`创建沙箱环境失败: ${error}`, {
        workflowId,
        nodeId
      });
      throw new Error(`创建沙箱环境失败: ${error}`);
    }
  }

  /**
   * 初始化沙箱环境
   */
  async initializeSandbox(sandboxPath: string, config: SandboxConfig): Promise<void> {
    try {
      // 验证沙箱路径
      if (!await this.isValidSandboxPath(sandboxPath)) {
        throw new Error(`无效的沙箱路径: ${sandboxPath}`);
      }

      // 更新配置
      this.activeSandboxes.set(sandboxPath, config);

      // 设置文件系统权限
      await this.setFileSystemPermissions(sandboxPath, config.permissions);

      // 创建安全策略
      await this.createSecurityPolicy(sandboxPath, config);

      // 创建初始文件结构
      await this.createInitialStructure(sandboxPath);

      logger.info(`沙箱环境初始化完成: ${sandboxPath}`, {
        nodeId: config.nodeId,
        workflowId: config.workflowId
      });
    } catch (error) {
      logger.error(`初始化沙箱环境失败: ${error}`, {
        sandboxPath
      });
      throw new Error(`初始化沙箱环境失败: ${error}`);
    }
  }

  /**
   * 清理沙箱环境
   */
  async cleanupSandbox(sandboxPath: string): Promise<void> {
    try {
      // 验证沙箱路径
      if (!await this.isValidSandboxPath(sandboxPath)) {
        logger.warn(`尝试清理无效的沙箱路径: ${sandboxPath}`);
        return;
      }

      // 获取沙箱统计信息（用于日志记录）
      const stats = await this.getSandboxStats(sandboxPath);

      // 删除沙箱目录
      await fs.rm(sandboxPath, { recursive: true, force: true });

      // 从活跃沙箱列表中移除
      this.activeSandboxes.delete(sandboxPath);
      this.securityPolicies.delete(sandboxPath);

      logger.info(`沙箱环境已清理: ${sandboxPath}`, {
        fileCount: stats.fileCount,
        size: stats.size
      });
    } catch (error) {
      logger.error(`清理沙箱环境失败: ${error}`, {
        sandboxPath
      });
      throw new Error(`清理沙箱环境失败: ${error}`);
    }
  }

  /**
   * 验证沙箱访问权限
   */
  validateSandboxAccess(sandboxPath: string, operation: 'read' | 'write' | 'execute'): boolean {
    try {
      const config = this.activeSandboxes.get(sandboxPath);
      if (!config) {
        logger.warn(`未找到沙箱配置: ${sandboxPath}`);
        return false;
      }

      const hasPermission = config.permissions[operation];
      
      if (!hasPermission) {
        logger.warn(`沙箱访问被拒绝: ${operation} 操作在 ${sandboxPath}`, {
          nodeId: config.nodeId,
          workflowId: config.workflowId
        });
      }

      return hasPermission;
    } catch (error) {
      logger.error(`验证沙箱访问权限失败: ${error}`, {
        sandboxPath,
        operation
      });
      return false;
    }
  }

  /**
   * 获取沙箱统计信息
   */
  async getSandboxStats(sandboxPath: string): Promise<SandboxStats> {
    try {
      const config = this.activeSandboxes.get(sandboxPath);
      const stats = await fs.stat(sandboxPath);
      const fileCount = await this.countFiles(sandboxPath);
      const size = await this.calculateDirectorySize(sandboxPath);

      return {
        path: sandboxPath,
        size,
        fileCount,
        createdAt: stats.birthtime,
        lastAccessed: stats.atime,
        permissions: config?.permissions || {
          read: false,
          write: false,
          execute: false
        }
      };
    } catch (error) {
      logger.error(`获取沙箱统计信息失败: ${error}`, {
        sandboxPath
      });
      throw new Error(`获取沙箱统计信息失败: ${error}`);
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * 验证沙箱路径是否有效
   */
  private async isValidSandboxPath(sandboxPath: string): Promise<boolean> {
    try {
      // 检查路径是否在基础路径下
      const resolvedPath = path.resolve(sandboxPath);
      const resolvedBasePath = path.resolve(this.basePath);
      
      if (!resolvedPath.startsWith(resolvedBasePath)) {
        return false;
      }

      // 检查路径是否存在
      await fs.access(sandboxPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 设置文件系统权限
   */
  private async setFileSystemPermissions(
    sandboxPath: string, 
    permissions: { read: boolean; write: boolean; execute: boolean }
  ): Promise<void> {
    try {
      // 为了确保可以在沙箱内创建文件，我们需要给目录适当的权限
      // 目录权限需要包含执行权限才能进入目录
      let mode = 0o700; // 默认给所有者完整权限
      
      // 在实际生产环境中，这里可以根据需要设置更严格的权限
      // 但在测试环境中，我们需要确保基本的文件操作可以进行
      
      await fs.chmod(sandboxPath, mode);
      
      logger.debug(`文件系统权限已设置: ${sandboxPath}`, {
        permissions,
        mode: mode.toString(8)
      });
    } catch (error) {
      logger.error(`设置文件系统权限失败: ${error}`, {
        sandboxPath,
        permissions
      });
      // 在某些环境中权限设置可能失败，但不应该阻止沙箱创建
      logger.warn(`权限设置失败，继续创建沙箱: ${error}`);
    }
  }

  /**
   * 创建安全策略
   */
  private async createSecurityPolicy(sandboxPath: string, config: SandboxConfig): Promise<void> {
    const policy: SecurityPolicy = {
      allowedPaths: [sandboxPath],
      blockedPaths: [
        '/etc',
        '/usr',
        '/bin',
        '/sbin',
        '/var',
        '/tmp',
        process.env.HOME || '/home'
      ],
      maxFileOperations: 1000,
      resourceLimits: config.resourceLimits
    };

    this.securityPolicies.set(sandboxPath, policy);
    
    // 创建安全策略文件
    const policyPath = path.join(sandboxPath, '.security_policy.json');
    await fs.writeFile(policyPath, JSON.stringify(policy, null, 2));
  }

  /**
   * 创建初始文件结构
   */
  private async createInitialStructure(sandboxPath: string): Promise<void> {
    // 创建基本目录结构
    const directories = ['input', 'output', 'temp', 'logs'];
    
    for (const dir of directories) {
      await fs.mkdir(path.join(sandboxPath, dir), { recursive: true });
    }

    // 创建README文件
    const readmeContent = `# 沙箱环境

这是一个隔离的执行环境，用于智能体安全执行任务。

## 目录结构
- input/: 输入文件目录
- output/: 输出文件目录  
- temp/: 临时文件目录
- logs/: 日志文件目录

## 安全限制
- 只能访问当前沙箱目录
- 文件操作受到权限控制
- 资源使用受到限制

创建时间: ${new Date().toISOString()}
`;

    await fs.writeFile(path.join(sandboxPath, 'README.md'), readmeContent);
  }

  /**
   * 计算目录中的文件数量
   */
  private async countFiles(dirPath: string): Promise<number> {
    try {
      let count = 0;
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        if (item.isFile()) {
          count++;
        } else if (item.isDirectory()) {
          count += await this.countFiles(path.join(dirPath, item.name));
        }
      }
      
      return count;
    } catch {
      return 0;
    }
  }

  /**
   * 计算目录大小
   */
  private async calculateDirectorySize(dirPath: string): Promise<number> {
    try {
      let size = 0;
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        if (item.isFile()) {
          const stats = await fs.stat(itemPath);
          size += stats.size;
        } else if (item.isDirectory()) {
          size += await this.calculateDirectorySize(itemPath);
        }
      }
      
      return size;
    } catch {
      return 0;
    }
  }

  /**
   * 获取所有活跃的沙箱
   */
  getActiveSandboxes(): Map<string, SandboxConfig> {
    return new Map(this.activeSandboxes);
  }

  /**
   * 清理所有沙箱
   */
  async cleanupAllSandboxes(): Promise<void> {
    const sandboxPaths = Array.from(this.activeSandboxes.keys());
    
    for (const sandboxPath of sandboxPaths) {
      try {
        await this.cleanupSandbox(sandboxPath);
      } catch (error) {
        logger.error(`清理沙箱失败: ${sandboxPath}`, { error });
      }
    }
  }
}

/**
 * 安全策略接口
 */
interface SecurityPolicy {
  allowedPaths: string[];
  blockedPaths: string[];
  maxFileOperations: number;
  resourceLimits: {
    maxMemory: number;
    maxExecutionTime: number;
    maxFileSize: number;
  };
}