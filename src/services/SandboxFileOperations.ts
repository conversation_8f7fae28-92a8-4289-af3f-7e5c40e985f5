import * as fs from 'fs/promises';
import * as path from 'path';
import { SandboxSecurityValidator, ValidationResult, FileOperation } from './SandboxSecurityValidator';
import { SandboxConfig } from '../types';
import { logger } from '../utils/logger';

/**
 * 沙箱文件操作包装器
 * 提供安全的文件操作接口，所有操作都经过安全验证
 */
export class SandboxFileOperations {
  private readonly validator: SandboxSecurityValidator;
  private readonly config: SandboxConfig;
  private operationCount: number = 0;
  private readonly maxOperations: number = 1000;

  constructor(config: SandboxConfig) {
    this.config = config;
    this.validator = new SandboxSecurityValidator();
  }

  /**
   * 安全读取文件
   */
  async readFile(filePath: string, encoding: BufferEncoding = 'utf-8'): Promise<string> {
    await this.validateOperation('read', filePath);
    
    try {
      const content = await fs.readFile(filePath, encoding);
      this.incrementOperationCount();
      
      logger.debug(`文件读取成功: ${filePath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId,
        size: content.length
      });
      
      return content;
    } catch (error) {
      logger.error(`文件读取失败: ${filePath}`, { error });
      throw new Error(`文件读取失败: ${error}`);
    }
  }

  /**
   * 安全写入文件
   */
  async writeFile(filePath: string, content: string, encoding: BufferEncoding = 'utf-8'): Promise<void> {
    await this.validateOperation('write', filePath);
    
    // 检查内容大小
    const contentSize = Buffer.byteLength(content, encoding);
    if (contentSize > this.config.resourceLimits.maxFileSize) {
      throw new Error(`文件内容超出大小限制: ${contentSize} > ${this.config.resourceLimits.maxFileSize}`);
    }

    try {
      // 确保目录存在
      const dirPath = path.dirname(filePath);
      await this.ensureDirectoryExists(dirPath);
      
      await fs.writeFile(filePath, content, encoding);
      this.incrementOperationCount();
      
      logger.debug(`文件写入成功: ${filePath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId,
        size: contentSize
      });
    } catch (error) {
      logger.error(`文件写入失败: ${filePath}`, { error });
      throw new Error(`文件写入失败: ${error}`);
    }
  }

  /**
   * 安全追加文件内容
   */
  async appendFile(filePath: string, content: string, encoding: BufferEncoding = 'utf-8'): Promise<void> {
    await this.validateOperation('write', filePath);
    
    try {
      await fs.appendFile(filePath, content, encoding);
      this.incrementOperationCount();
      
      logger.debug(`文件追加成功: ${filePath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    } catch (error) {
      logger.error(`文件追加失败: ${filePath}`, { error });
      throw new Error(`文件追加失败: ${error}`);
    }
  }

  /**
   * 安全删除文件
   */
  async deleteFile(filePath: string): Promise<void> {
    await this.validateOperation('delete', filePath);
    
    try {
      await fs.unlink(filePath);
      this.incrementOperationCount();
      
      logger.debug(`文件删除成功: ${filePath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    } catch (error) {
      logger.error(`文件删除失败: ${filePath}`, { error });
      throw new Error(`文件删除失败: ${error}`);
    }
  }

  /**
   * 安全创建目录
   */
  async createDirectory(dirPath: string): Promise<void> {
    await this.validateOperation('write', dirPath);
    
    try {
      await fs.mkdir(dirPath, { recursive: true });
      this.incrementOperationCount();
      
      logger.debug(`目录创建成功: ${dirPath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    } catch (error) {
      logger.error(`目录创建失败: ${dirPath}`, { error });
      throw new Error(`目录创建失败: ${error}`);
    }
  }

  /**
   * 安全删除目录
   */
  async deleteDirectory(dirPath: string): Promise<void> {
    await this.validateOperation('delete', dirPath);
    
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
      this.incrementOperationCount();
      
      logger.debug(`目录删除成功: ${dirPath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    } catch (error) {
      logger.error(`目录删除失败: ${dirPath}`, { error });
      throw new Error(`目录删除失败: ${error}`);
    }
  }

  /**
   * 安全列出目录内容
   */
  async listDirectory(dirPath: string): Promise<string[]> {
    await this.validateOperation('read', dirPath);
    
    try {
      const items = await fs.readdir(dirPath);
      this.incrementOperationCount();
      
      logger.debug(`目录列表获取成功: ${dirPath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId,
        itemCount: items.length
      });
      
      return items;
    } catch (error) {
      logger.error(`目录列表获取失败: ${dirPath}`, { error });
      throw new Error(`目录列表获取失败: ${error}`);
    }
  }

  /**
   * 安全获取文件统计信息
   */
  async getFileStats(filePath: string): Promise<FileStats> {
    await this.validateOperation('read', filePath);
    
    try {
      const stats = await fs.stat(filePath);
      this.incrementOperationCount();
      
      const fileStats: FileStats = {
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        accessedAt: stats.atime
      };
      
      logger.debug(`文件统计信息获取成功: ${filePath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId,
        size: fileStats.size
      });
      
      return fileStats;
    } catch (error) {
      logger.error(`文件统计信息获取失败: ${filePath}`, { error });
      throw new Error(`文件统计信息获取失败: ${error}`);
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      await this.validateOperation('read', filePath);
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 复制文件
   */
  async copyFile(sourcePath: string, destPath: string): Promise<void> {
    await this.validateOperation('read', sourcePath);
    await this.validateOperation('write', destPath);
    
    try {
      await fs.copyFile(sourcePath, destPath);
      this.incrementOperationCount();
      
      logger.debug(`文件复制成功: ${sourcePath} -> ${destPath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    } catch (error) {
      logger.error(`文件复制失败: ${sourcePath} -> ${destPath}`, { error });
      throw new Error(`文件复制失败: ${error}`);
    }
  }

  /**
   * 移动文件
   */
  async moveFile(sourcePath: string, destPath: string): Promise<void> {
    await this.validateOperation('read', sourcePath);
    await this.validateOperation('write', destPath);
    await this.validateOperation('delete', sourcePath);
    
    try {
      await fs.rename(sourcePath, destPath);
      this.incrementOperationCount();
      
      logger.debug(`文件移动成功: ${sourcePath} -> ${destPath}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    } catch (error) {
      logger.error(`文件移动失败: ${sourcePath} -> ${destPath}`, { error });
      throw new Error(`文件移动失败: ${error}`);
    }
  }

  /**
   * 获取操作统计信息
   */
  getOperationStats(): OperationStats {
    return {
      operationCount: this.operationCount,
      maxOperations: this.maxOperations,
      remainingOperations: this.maxOperations - this.operationCount,
      utilizationRate: (this.operationCount / this.maxOperations) * 100
    };
  }

  /**
   * 重置操作计数
   */
  resetOperationCount(): void {
    this.operationCount = 0;
    logger.debug('操作计数已重置', {
      nodeId: this.config.nodeId,
      workflowId: this.config.workflowId
    });
  }

  /**
   * 验证操作
   */
  private async validateOperation(operation: FileOperation, filePath: string): Promise<void> {
    // 检查操作次数限制
    if (this.operationCount >= this.maxOperations) {
      throw new Error(`操作次数超出限制: ${this.operationCount} >= ${this.maxOperations}`);
    }

    // 检查权限
    if (!this.config.permissions[operation === 'delete' ? 'write' : operation]) {
      throw new Error(`没有${operation}操作权限`);
    }

    // 安全验证
    const validation = await this.validator.validateFileOperation(
      operation,
      filePath,
      this.config.basePath,
      this.config.resourceLimits.maxFileSize
    );

    if (!validation.isValid) {
      const errorMessage = `文件操作安全验证失败: ${validation.violations.join(', ')}`;
      logger.warn(errorMessage, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId,
        operation,
        filePath,
        violations: validation.violations
      });
      throw new Error(errorMessage);
    }

    // 记录警告
    if (validation.warnings.length > 0) {
      logger.warn(`文件操作警告: ${validation.warnings.join(', ')}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId,
        operation,
        filePath,
        warnings: validation.warnings
      });
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * 增加操作计数
   */
  private incrementOperationCount(): void {
    this.operationCount++;
    
    // 当接近限制时发出警告
    if (this.operationCount >= this.maxOperations * 0.9) {
      logger.warn(`操作次数接近限制: ${this.operationCount}/${this.maxOperations}`, {
        nodeId: this.config.nodeId,
        workflowId: this.config.workflowId
      });
    }
  }
}

/**
 * 文件统计信息接口
 */
export interface FileStats {
  size: number;
  isFile: boolean;
  isDirectory: boolean;
  createdAt: Date;
  modifiedAt: Date;
  accessedAt: Date;
}

/**
 * 操作统计信息接口
 */
export interface OperationStats {
  operationCount: number;
  maxOperations: number;
  remainingOperations: number;
  utilizationRate: number;
}