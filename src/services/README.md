# 沙箱环境管理系统

本目录包含了多智能体系统的沙箱环境管理实现，确保每个智能体在隔离和安全的环境中执行任务。

## 核心组件

### 1. SandboxManager (沙箱管理器)
- **文件**: `SandboxManager.ts`
- **功能**: 
  - 创建和管理智能体沙箱环境
  - 初始化沙箱目录结构
  - 设置文件系统权限
  - 清理沙箱资源
  - 统计沙箱使用情况

### 2. SandboxSecurityValidator (安全验证器)
- **文件**: `SandboxSecurityValidator.ts`
- **功能**:
  - 验证文件路径安全性
  - 检测路径遍历攻击
  - 验证文件操作权限
  - 扫描恶意内容模式
  - 检测敏感信息泄露

### 3. SandboxFileOperations (文件操作包装器)
- **文件**: `SandboxFileOperations.ts`
- **功能**:
  - 提供安全的文件操作接口
  - 操作次数限制和统计
  - 文件大小限制检查
  - 自动目录创建
  - 操作日志记录

## 安全特性

### 路径安全
- ✅ 防止路径遍历攻击 (`../`, `~`, 绝对路径)
- ✅ 限制文件访问范围在沙箱内
- ✅ 检测危险路径模式
- ✅ 文件名长度和字符验证

### 文件类型安全
- ✅ 禁止危险文件扩展名 (`.exe`, `.dll`, `.bat` 等)
- ✅ 限制可执行文件类型
- ✅ 二进制文件检测和处理

### 内容安全
- ✅ 恶意脚本模式检测 (`eval`, `exec`, `system` 等)
- ✅ 敏感信息检测 (密码, API密钥, 邮箱等)
- ✅ 文件大小限制
- ✅ 操作次数限制

### 权限控制
- ✅ 细粒度权限控制 (读/写/执行)
- ✅ 资源使用限制 (内存, 执行时间, 文件大小)
- ✅ 操作审计和日志

## 使用示例

```typescript
import { SandboxManagerImpl } from './services/SandboxManager';
import { SandboxFileOperations } from './services/SandboxFileOperations';

// 1. 创建沙箱管理器
const sandboxManager = new SandboxManagerImpl('./sandboxes');

// 2. 为智能体创建沙箱
const sandboxPath = await sandboxManager.createSandbox('workflow-001', 'agent_a');

// 3. 配置沙箱环境
const config = {
  basePath: sandboxPath,
  nodeId: 'agent_a',
  workflowId: 'workflow-001',
  permissions: {
    read: true,
    write: true,
    execute: false
  },
  resourceLimits: {
    maxMemory: 512 * 1024 * 1024, // 512MB
    maxExecutionTime: 300000, // 5分钟
    maxFileSize: 100 * 1024 * 1024 // 100MB
  }
};

await sandboxManager.initializeSandbox(sandboxPath, config);

// 4. 安全文件操作
const fileOps = new SandboxFileOperations(config);

// 写入文件
await fileOps.writeFile('input/data.json', JSON.stringify({
  task: 'process',
  data: [1, 2, 3]
}));

// 读取文件
const content = await fileOps.readFile('input/data.json');

// 获取统计信息
const stats = fileOps.getOperationStats();
console.log(`操作次数: ${stats.operationCount}/${stats.maxOperations}`);

// 5. 清理沙箱
await sandboxManager.cleanupSandbox(sandboxPath);
```

## 目录结构

每个沙箱环境包含以下标准目录结构：

```
sandbox/
├── input/          # 输入文件目录
├── output/         # 输出文件目录
├── temp/           # 临时文件目录
├── logs/           # 日志文件目录
├── README.md       # 沙箱说明文档
└── .security_policy.json  # 安全策略配置
```

## 测试覆盖

- ✅ 沙箱管理器功能测试 (`SandboxManager.test.ts`)
- ✅ 安全验证器测试 (`SandboxSecurityValidator.test.ts`)
- ✅ 文件操作测试 (`SandboxFileOperations.test.ts`)
- ✅ 边界情况和错误处理测试
- ✅ 性能和并发测试

## 配置选项

### 权限配置
```typescript
permissions: {
  read: boolean,    // 读取权限
  write: boolean,   // 写入权限
  execute: boolean  // 执行权限
}
```

### 资源限制
```typescript
resourceLimits: {
  maxMemory: number,        // 最大内存使用 (字节)
  maxExecutionTime: number, // 最大执行时间 (毫秒)
  maxFileSize: number       // 最大文件大小 (字节)
}
```

## 最佳实践

1. **最小权限原则**: 只授予智能体必需的权限
2. **资源限制**: 设置合理的资源使用上限
3. **定期清理**: 及时清理不再使用的沙箱环境
4. **监控审计**: 记录和监控文件操作活动
5. **安全更新**: 定期更新安全策略和检测规则

## 性能考虑

- 沙箱创建: ~10-50ms (取决于文件系统性能)
- 文件操作验证: ~1-5ms (取决于文件大小和复杂度)
- 内容安全扫描: ~10-100ms (取决于文件大小)
- 并发支持: 支持多个沙箱同时运行

## 故障排除

### 常见问题

1. **权限错误**: 检查文件系统权限和沙箱配置
2. **路径错误**: 确保文件路径在沙箱范围内
3. **大小限制**: 检查文件大小是否超出限制
4. **操作限制**: 检查是否达到操作次数上限

### 调试技巧

- 启用详细日志记录
- 检查沙箱统计信息
- 验证安全策略配置
- 测试文件操作权限