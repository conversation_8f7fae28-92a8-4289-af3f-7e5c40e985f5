# 服务器配置
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# MongoDB 配置
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=langgraph_multi_agent

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# LLM 配置
LLM_PROVIDER=openai
LLM_API_KEY=your_api_key_here
LLM_MODEL=gpt-3.5-turbo

# 沙箱配置
SANDBOX_BASE_PATH=./sandbox
SANDBOX_MAX_EXECUTION_TIME=30000

# 监控配置
ENABLE_MONITORING=true
ALERT_WEBHOOK_URL=