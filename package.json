{"name": "langgraph-multi-agent-mvp", "version": "1.0.0", "description": "Multi-agent system MVP using LangChain.js and LangGraph", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/scripts/start-dev.ts", "dev:simple": "ts-node src/index.ts", "test": "vitest --run", "test:watch": "vitest", "lint": "echo '<PERSON><PERSON> not configured yet'", "clean": "rm -rf dist logs/*.log"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "joi": "^17.11.0", "uuid": "^9.0.1", "winston": "^3.11.0", "mongodb": "^6.3.0", "redis": "^4.6.10", "@langchain/core": "^0.3.58", "@langchain/langgraph": "^0.4.5", "langchain": "^0.3.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "typescript": "^5.3.0", "ts-node": "^10.9.0", "vitest": "^1.0.0", "supertest": "^6.3.3", "@types/supertest": "^6.0.2"}}